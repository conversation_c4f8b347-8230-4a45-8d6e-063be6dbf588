<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Statistics - ISA Meal Vote</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-firestore-compat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="./firebase-config.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-6 border-t-4 border-blue-600">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 rounded-xl shadow-md bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center border-2 border-red-500">
                        <div class="text-center">
                            <div class="text-white font-bold text-sm">ISA</div>
                            <div class="text-xs">📊</div>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">Admin Statistics</h1>
                        <p class="text-gray-600">Real-time voting analytics</p>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <a href="./admin-dashboard.html" class="bg-indigo-600 text-white px-3 py-2 rounded-lg hover:bg-indigo-700 text-sm">
                        👨‍💼 Dashboard
                    </a>
                    <a href="./quick-admin.html" class="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 text-sm">
                        Create Survey
                    </a>
                    <a href="https://isa-meal-vote.web.app" class="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 text-sm">
                        View App
                    </a>
                </div>
            </div>
        </div>

        <!-- Auto Refresh Toggle -->
        <div class="bg-white rounded-lg shadow p-4 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="font-semibold text-gray-800">Auto Refresh</h3>
                    <p class="text-sm text-gray-600">Updates every 30 seconds</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="autoRefresh" class="sr-only peer" checked>
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
            </div>
        </div>

        <!-- Loading State -->
        <div id="loading" class="text-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Loading statistics...</p>
        </div>

        <!-- No Data State -->
        <div id="noData" class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center" style="display: none;">
            <div class="text-4xl mb-4">📊</div>
            <h3 class="text-lg font-semibold text-yellow-800 mb-2">No Active Surveys</h3>
            <p class="text-yellow-700 mb-4">Create a survey to start collecting votes and statistics.</p>
            <a href="./quick-admin.html" class="bg-yellow-600 text-white px-6 py-2 rounded-lg hover:bg-yellow-700">
                Create Survey
            </a>
        </div>

        <!-- Statistics Content -->
        <div id="statsContent" style="display: none;">
            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <span class="text-2xl">📊</span>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Votes</p>
                            <p class="text-2xl font-semibold text-gray-900" id="totalVotes">0</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <span class="text-2xl">✅</span>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Confirmed Participants</p>
                            <p class="text-2xl font-semibold text-gray-900" id="confirmedCount">0</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <span class="text-2xl">⏳</span>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Pending Confirmation</p>
                            <p class="text-2xl font-semibold text-gray-900" id="pendingCount">0</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100 text-red-600">
                            <span class="text-2xl">❌</span>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Won't Participate</p>
                            <p class="text-2xl font-semibold text-gray-900" id="declinedCount">0</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Survey Details -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- Current Survey Info -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Current Survey</h3>
                    <div id="surveyInfo">
                        <h4 class="font-semibold text-gray-900 mb-2" id="surveyTitle">Loading...</h4>
                        <p class="text-gray-600 mb-3" id="surveyDescription">Loading...</p>
                        <div class="text-sm text-gray-500">
                            <p>Deadline: <span id="surveyDeadline">Loading...</span></p>
                            <p>Threshold: <span id="surveyThreshold">Loading...</span> votes needed</p>
                        </div>
                    </div>
                </div>

                <!-- Progress Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Voting Progress</h3>
                    <canvas id="progressChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Detailed Results -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Detailed Results</h3>
                <div id="detailedResults">
                    <!-- Results will be populated here -->
                </div>
            </div>
        </div>

        <!-- Last Updated -->
        <div class="text-center text-sm text-gray-500 mt-6">
            Last updated: <span id="lastUpdated">Never</span>
        </div>
    </div>

    <script>
        const app = firebase.initializeApp(window.firebaseConfig);
        const db = firebase.firestore();
        
        let autoRefreshInterval;
        let progressChart;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            setupAutoRefresh();
        });

        function setupAutoRefresh() {
            const toggle = document.getElementById('autoRefresh');
            
            function startAutoRefresh() {
                if (autoRefreshInterval) clearInterval(autoRefreshInterval);
                autoRefreshInterval = setInterval(loadStatistics, 30000); // 30 seconds
            }

            function stopAutoRefresh() {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                    autoRefreshInterval = null;
                }
            }

            toggle.addEventListener('change', function() {
                if (this.checked) {
                    startAutoRefresh();
                } else {
                    stopAutoRefresh();
                }
            });

            // Start auto refresh by default
            if (toggle.checked) {
                startAutoRefresh();
            }
        }

        async function loadStatistics() {
            try {
                console.log('Loading statistics...');
                document.getElementById('loading').style.display = 'block';
                document.getElementById('noData').style.display = 'none';
                document.getElementById('statsContent').style.display = 'none';

                // Get all surveys
                const surveysSnap = await db.collection('surveys').get();
                console.log('Found surveys:', surveysSnap.size);

                if (surveysSnap.empty) {
                    console.log('No surveys found');
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('noData').style.display = 'block';
                    return;
                }

                // Get the most recent survey (should be only one active)
                const surveys = surveysSnap.docs.map(d => ({ id: d.id, ...d.data() }));
                console.log('Parsed surveys:', surveys);

                surveys.sort((a, b) => {
                    const dateA = a.createdAt ? a.createdAt.toDate() : new Date(0);
                    const dateB = b.createdAt ? b.createdAt.toDate() : new Date(0);
                    return dateB - dateA;
                });

                const currentSurvey = surveys[0];
                console.log('Current survey:', currentSurvey);

                // Get votes for current survey
                const votesSnap = await db.collection('surveys').doc(currentSurvey.id).collection('votes').get();
                const votes = votesSnap.docs.map(d => ({ id: d.id, ...d.data() }));
                console.log('Votes found:', votes.length);

                // Calculate statistics
                const stats = calculateStatistics(currentSurvey, votes);
                console.log('Calculated stats:', stats);

                // Update UI
                updateUI(currentSurvey, stats);
                
                document.getElementById('loading').style.display = 'none';
                document.getElementById('statsContent').style.display = 'block';
                document.getElementById('lastUpdated').textContent = new Date().toLocaleTimeString();

            } catch (error) {
                console.error('Error loading statistics:', error);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('noData').style.display = 'block';
                document.getElementById('noData').innerHTML = `
                    <div class="text-4xl mb-4">⚠️</div>
                    <h3 class="text-lg font-semibold text-red-800 mb-2">Error Loading Statistics</h3>
                    <p class="text-red-700 mb-4">${error.message}</p>
                    <button onclick="loadStatistics()" class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700">
                        Retry
                    </button>
                `;
            }
        }

        function calculateStatistics(survey, votes) {
            const totalVotes = votes.length;
            const confirmedCount = votes.filter(v => v.participate === true).length;
            const declinedCount = votes.filter(v => v.participate === false).length;
            const pendingCount = votes.filter(v => v.participate === null || v.participate === undefined).length;
            const threshold = survey.threshold || 0;
            const thresholdProgress = threshold > 0 ? Math.min((totalVotes / threshold) * 100, 100) : 0;

            // Count votes per option
            const optionCounts = {};
            survey.options.forEach(option => {
                optionCounts[option.id] = {
                    text: option.text,
                    votes: 0,
                    confirmed: 0,
                    declined: 0,
                    pending: 0
                };
            });

            votes.forEach(vote => {
                if (optionCounts[vote.optionId]) {
                    optionCounts[vote.optionId].votes++;
                    if (vote.participate === true) {
                        optionCounts[vote.optionId].confirmed++;
                    } else if (vote.participate === false) {
                        optionCounts[vote.optionId].declined++;
                    } else {
                        optionCounts[vote.optionId].pending++;
                    }
                }
            });

            return {
                totalVotes,
                confirmedCount,
                declinedCount,
                pendingCount,
                threshold,
                thresholdProgress,
                optionCounts,
                thresholdMet: totalVotes >= threshold
            };
        }

        function updateUI(survey, stats) {
            // Update summary cards
            document.getElementById('totalVotes').textContent = stats.totalVotes;
            document.getElementById('confirmedCount').textContent = stats.confirmedCount;
            document.getElementById('pendingCount').textContent = stats.pendingCount;
            document.getElementById('declinedCount').textContent = stats.declinedCount;

            // Update survey info
            document.getElementById('surveyTitle').textContent = survey.title;
            document.getElementById('surveyDescription').textContent = survey.description;
            document.getElementById('surveyDeadline').textContent = survey.deadline ? 
                survey.deadline.toDate().toLocaleString() : 'Not set';
            document.getElementById('surveyThreshold').textContent = stats.threshold;

            // Update progress chart
            updateProgressChart(stats);

            // Update detailed results
            updateDetailedResults(stats);
        }

        function updateProgressChart(stats) {
            const ctx = document.getElementById('progressChart').getContext('2d');
            
            if (progressChart) {
                progressChart.destroy();
            }

            progressChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Votes Received', 'Votes Needed'],
                    datasets: [{
                        data: [stats.totalVotes, Math.max(0, stats.threshold - stats.totalVotes)],
                        backgroundColor: [
                            stats.thresholdMet ? '#10B981' : '#3B82F6',
                            '#E5E7EB'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updateDetailedResults(stats) {
            const container = document.getElementById('detailedResults');
            container.innerHTML = '';

            Object.entries(stats.optionCounts).forEach(([optionId, data], index) => {
                const percentage = stats.totalVotes > 0 ? (data.votes / stats.totalVotes * 100) : 0;
                const confirmationRate = data.votes > 0 ? (data.confirmed / data.votes * 100) : 0;

                const resultDiv = document.createElement('div');
                resultDiv.className = 'mb-4 p-4 border rounded-lg';
                resultDiv.innerHTML = `
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="font-semibold text-gray-800">${index + 1}. ${data.text}</h4>
                        <div class="text-right">
                            <span class="text-lg font-bold text-blue-600">${data.votes} votes</span>
                            <span class="text-sm text-gray-500">(${percentage.toFixed(1)}%)</span>
                        </div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: ${percentage}%"></div>
                    </div>
                    <div class="grid grid-cols-3 gap-2 text-sm">
                        <div class="bg-green-50 p-2 rounded text-center">
                            <div class="font-semibold text-green-700">✅ ${data.confirmed}</div>
                            <div class="text-green-600 text-xs">Confirmed</div>
                        </div>
                        <div class="bg-yellow-50 p-2 rounded text-center">
                            <div class="font-semibold text-yellow-700">⏳ ${data.pending}</div>
                            <div class="text-yellow-600 text-xs">Pending</div>
                        </div>
                        <div class="bg-red-50 p-2 rounded text-center">
                            <div class="font-semibold text-red-700">❌ ${data.declined}</div>
                            <div class="text-red-600 text-xs">Declined</div>
                        </div>
                    </div>
                    <div class="mt-2 text-center text-sm text-gray-600">
                        <span>📊 ${confirmationRate.toFixed(1)}% confirmation rate</span>
                    </div>
                `;
                container.appendChild(resultDiv);
            });
        }
    </script>
</body>
</html>
