// Daily Survey Manager for ISA Meal Vote Platform
// Automatically creates and manages daily meal surveys

class DailySurveyManager {
    constructor() {
        this.db = firebase.firestore();
        this.surveyStartTime = { hour: 8, minute: 30 }; // 08:30
        this.surveyEndTime = { hour: 13, minute: 30 };   // 13:30
        this.participationEndTime = { hour: 23, minute: 59 }; // 23:59
        
        // Tunisian meal options for different days
        this.weeklyMenus = {
            monday: [
                { text: "Couscous Tunisien aux Légumes", image: "couscous.jpg" },
                { text: "Brik à l'Œuf et Thon", image: "brik.jpg" },
                { text: "Tajine de Poulet aux Olives", image: "tajine.jpg" },
                { text: "<PERSON><PERSON><PERSON> (Pâtes Tunisiennes)", image: "rechta.jpg" },
                { text: "Salade Mechouia", image: "mechouia.jpg" }
            ],
            tuesday: [
                { text: "Couscous au Poisson", image: "couscous_fish.jpg" },
                { text: "Brik aux Pommes de Terre", image: "brik_potato.jpg" },
                { text: "<PERSON><PERSON><PERSON> (<PERSON>upe <PERSON>)", image: "chorba.jpg" },
                { text: "Makloubeh", image: "makloubeh.jpg" },
                { text: "Sandwich Tunisien", image: "sandwich.jpg" }
            ],
            wednesday: [
                { text: "Couscous à l'Agneau", image: "couscous_lamb.jpg" },
                { text: "Brik au Fromage", image: "brik_cheese.jpg" },
                { text: "Tajine de Bœuf", image: "tajine_beef.jpg" },
                { text: "Riz au Poulet", image: "rice_chicken.jpg" },
                { text: "Salade Tunisienne", image: "salad.jpg" }
            ],
            thursday: [
                { text: "Couscous aux Fruits de Mer", image: "couscous_seafood.jpg" },
                { text: "Brik Mixte", image: "brik_mixed.jpg" },
                { text: "Tajine aux Légumes", image: "tajine_veg.jpg" },
                { text: "Pâtes à la Tunisienne", image: "pasta.jpg" },
                { text: "Casse-croûte Tunisien", image: "casse_croute.jpg" }
            ],
            friday: [
                { text: "Couscous Royal", image: "couscous_royal.jpg" },
                { text: "Brik Spécial Vendredi", image: "brik_friday.jpg" },
                { text: "Tajine de Poisson", image: "tajine_fish.jpg" },
                { text: "Rechta aux Fruits de Mer", image: "rechta_seafood.jpg" },
                { text: "Pizza Tunisienne", image: "pizza.jpg" }
            ],
            saturday: [
                { text: "Couscous du Weekend", image: "couscous_weekend.jpg" },
                { text: "Brik Weekend", image: "brik_weekend.jpg" },
                { text: "Tajine Weekend", image: "tajine_weekend.jpg" },
                { text: "Plat du Chef", image: "chef_special.jpg" },
                { text: "Surprise du Samedi", image: "saturday_surprise.jpg" }
            ]
        };
    }

    // Initialize the daily survey system
    async initialize() {
        console.log('🔄 Initializing Daily Survey Manager...');
        
        // Check and create today's survey if needed
        await this.checkAndCreateTodaySurvey();
        
        // Set up automatic survey management
        this.setupAutomaticManagement();
        
        console.log('✅ Daily Survey Manager initialized');
    }

    // Check if today's survey exists and create if needed
    async checkAndCreateTodaySurvey() {
        try {
            const today = new Date();
            const todayStr = this.formatDate(today);
            
            // Skip weekends (Sunday = 0, Saturday = 6)
            if (today.getDay() === 0) {
                console.log('📅 Sunday - No survey needed');
                return;
            }
            
            // Check if survey already exists for today
            const existingSurvey = await this.getTodaySurvey();
            
            if (existingSurvey) {
                console.log('✅ Today\'s survey already exists:', existingSurvey.title);
                await this.updateSurveyPhase(existingSurvey);
                return;
            }
            
            // Check if we should create a new survey (between 08:30 and 13:30)
            const currentTime = this.getCurrentTimeInMinutes();
            const startTime = this.surveyStartTime.hour * 60 + this.surveyStartTime.minute;
            const endTime = this.surveyEndTime.hour * 60 + this.surveyEndTime.minute;
            
            if (currentTime >= startTime && currentTime <= endTime) {
                await this.createTodaySurvey();
            } else if (currentTime < startTime) {
                console.log('⏰ Too early - Survey will be created at 08:30');
            } else {
                console.log('⏰ Too late - Survey creation time has passed');
            }
            
        } catch (error) {
            console.error('❌ Error checking/creating today\'s survey:', error);
        }
    }

    // Create today's survey
    async createTodaySurvey() {
        try {
            const today = new Date();
            const dayName = this.getDayName(today);
            const todayStr = this.formatDate(today);
            const tomorrowStr = this.formatDate(new Date(today.getTime() + 24 * 60 * 60 * 1000));
            
            // Get menu options for today
            const menuOptions = this.getMenuForDay(dayName);
            
            // Create survey data
            const surveyData = {
                title: `Tomorrow's Lunch Menu - ${tomorrowStr}`,
                description: `Vote for tomorrow's delicious meal! Voting closes at 13:30.`,
                options: menuOptions.map((option, index) => ({
                    id: `option_${Date.now()}_${index}`,
                    text: option.text,
                    image: option.image,
                    votes: 0
                })),
                createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                createdFor: todayStr,
                servingDate: tomorrowStr,
                phase: 'voting',
                votesRequired: 25,
                totalStudents: 100,
                votingDeadline: this.createDeadlineTimestamp(today, this.surveyEndTime),
                participationDeadline: this.createDeadlineTimestamp(today, this.participationEndTime),
                autoCreated: true,
                dayOfWeek: dayName
            };
            
            // Save to database
            const docRef = await this.db.collection('surveys').add(surveyData);
            
            console.log('✅ Created today\'s survey:', surveyData.title);
            console.log('📊 Survey ID:', docRef.id);
            
            return { id: docRef.id, ...surveyData };
            
        } catch (error) {
            console.error('❌ Error creating today\'s survey:', error);
            throw error;
        }
    }

    // Get today's survey if it exists
    async getTodaySurvey() {
        try {
            const todayStr = this.formatDate(new Date());
            
            const snapshot = await this.db.collection('surveys')
                .where('createdFor', '==', todayStr)
                .limit(1)
                .get();
            
            if (snapshot.empty) {
                return null;
            }
            
            const doc = snapshot.docs[0];
            return { id: doc.id, ...doc.data() };
            
        } catch (error) {
            console.error('❌ Error getting today\'s survey:', error);
            return null;
        }
    }

    // Update survey phase based on current time
    async updateSurveyPhase(survey) {
        try {
            const currentTime = this.getCurrentTimeInMinutes();
            const endTime = this.surveyEndTime.hour * 60 + this.surveyEndTime.minute;
            const participationEnd = this.participationEndTime.hour * 60 + this.participationEndTime.minute;
            
            let newPhase = survey.phase;
            let shouldUpdate = false;
            
            if (currentTime >= participationEnd) {
                newPhase = 'closed';
                shouldUpdate = survey.phase !== 'closed';
            } else if (currentTime >= endTime) {
                if (survey.phase === 'voting') {
                    newPhase = 'participation';
                    shouldUpdate = true;
                    
                    // Determine winning option
                    await this.determineWinningOption(survey);
                }
            }
            
            if (shouldUpdate) {
                await this.db.collection('surveys').doc(survey.id).update({
                    phase: newPhase,
                    updatedAt: firebase.firestore.FieldValue.serverTimestamp()
                });
                
                console.log(`📊 Updated survey phase: ${survey.phase} → ${newPhase}`);
            }
            
        } catch (error) {
            console.error('❌ Error updating survey phase:', error);
        }
    }

    // Determine winning option for a survey
    async determineWinningOption(survey) {
        try {
            const votesSnap = await this.db.collection('surveys').doc(survey.id).collection('votes').get();
            
            const optionVotes = {};
            let totalVotes = 0;
            let participants = 0;
            
            votesSnap.docs.forEach(doc => {
                const vote = doc.data();
                if (vote.optionId) {
                    optionVotes[vote.optionId] = (optionVotes[vote.optionId] || 0) + 1;
                    totalVotes++;
                }
                if (vote.participate === true) {
                    participants++;
                }
            });
            
            // Find winning option
            let winningOptionId = survey.options[0].id;
            let maxVotes = 0;
            
            for (const [optionId, votes] of Object.entries(optionVotes)) {
                if (votes > maxVotes) {
                    maxVotes = votes;
                    winningOptionId = optionId;
                }
            }
            
            const winningOption = survey.options.find(opt => opt.id === winningOptionId);
            
            // Update survey with results
            await this.db.collection('surveys').doc(survey.id).update({
                winningOption: winningOption,
                finalVoteCount: totalVotes,
                participantCount: participants,
                thresholdMet: totalVotes >= (survey.votesRequired || 25),
                votingClosedAt: firebase.firestore.FieldValue.serverTimestamp()
            });
            
            console.log('🏆 Winning option determined:', winningOption.text);
            
        } catch (error) {
            console.error('❌ Error determining winning option:', error);
        }
    }

    // Setup automatic survey management
    setupAutomaticManagement() {
        // Check every minute for survey updates
        setInterval(async () => {
            await this.checkAndCreateTodaySurvey();
        }, 60000); // 1 minute
        
        console.log('⏰ Automatic survey management started');
    }

    // Get menu options for a specific day
    getMenuForDay(dayName) {
        const day = dayName.toLowerCase();
        return this.weeklyMenus[day] || this.weeklyMenus.monday;
    }

    // Utility functions
    getCurrentTimeInMinutes() {
        const now = new Date();
        return now.getHours() * 60 + now.getMinutes();
    }

    formatDate(date) {
        return date.toISOString().split('T')[0]; // YYYY-MM-DD
    }

    getDayName(date) {
        const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        return days[date.getDay()];
    }

    createDeadlineTimestamp(date, time) {
        const deadline = new Date(date);
        deadline.setHours(time.hour, time.minute, 0, 0);
        return firebase.firestore.Timestamp.fromDate(deadline);
    }

    // Archive old surveys (older than 7 days)
    async archiveOldSurveys() {
        try {
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            
            const oldSurveys = await this.db.collection('surveys')
                .where('createdAt', '<', firebase.firestore.Timestamp.fromDate(weekAgo))
                .get();
            
            const batch = this.db.batch();
            
            oldSurveys.docs.forEach(doc => {
                // Move to archive collection
                batch.set(this.db.collection('archivedSurveys').doc(doc.id), doc.data());
                // Delete from main collection
                batch.delete(doc.ref);
            });
            
            if (!oldSurveys.empty) {
                await batch.commit();
                console.log(`🗄️ Archived ${oldSurveys.size} old surveys`);
            }
            
        } catch (error) {
            console.error('❌ Error archiving old surveys:', error);
        }
    }

    // Manual survey creation (for admin use)
    async createCustomSurvey(title, description, options) {
        try {
            const today = new Date();
            const todayStr = this.formatDate(today);
            
            const surveyData = {
                title: title,
                description: description,
                options: options.map((option, index) => ({
                    id: `option_${Date.now()}_${index}`,
                    text: option.text,
                    image: option.image || 'default.jpg',
                    votes: 0
                })),
                createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                createdFor: todayStr,
                phase: 'voting',
                votesRequired: 25,
                totalStudents: 100,
                votingDeadline: this.createDeadlineTimestamp(today, this.surveyEndTime),
                participationDeadline: this.createDeadlineTimestamp(today, this.participationEndTime),
                autoCreated: false,
                customCreated: true
            };
            
            const docRef = await this.db.collection('surveys').add(surveyData);
            
            console.log('✅ Created custom survey:', title);
            return { id: docRef.id, ...surveyData };
            
        } catch (error) {
            console.error('❌ Error creating custom survey:', error);
            throw error;
        }
    }
}

// Create global instance
window.DailySurveyManager = new DailySurveyManager();

// Auto-initialize when Firebase is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for Firebase to initialize
    setTimeout(() => {
        if (window.firebase && window.firebaseConfig) {
            window.DailySurveyManager.initialize();
        }
    }, 1000);
});

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DailySurveyManager;
}
