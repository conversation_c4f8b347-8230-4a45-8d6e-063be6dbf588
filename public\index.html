<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ISA Meal Vote - University Restaurant</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
    <div id="root"></div>

    <!-- React UMD -->
    <script src="https://unpkg.com/react@18/umd/react.production.min.js" crossorigin></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js" crossorigin></script>

    <!-- Firebase compat UMD (no build tools needed) -->
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-firestore-compat.js"></script>

    <!-- Images and assets -->
    <script src="./images.js"></script>

    <!-- Your Firebase config (fill in values) -->
    <script src="./firebase-config.js"></script>

    <!-- SMS Service -->
    <script src="./sms-service.js"></script>

    <!-- Daily Survey Manager -->
    <script src="./daily-survey-manager.js"></script>

    <!-- App logic -->
    <script src="./app.js"></script>
  </body>
</html>

