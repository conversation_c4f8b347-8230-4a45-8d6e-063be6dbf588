<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - ISA Meal Vote</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-firestore-compat.js"></script>
    <script src="./firebase-config.js"></script>
    <script src="./sms-service.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 rounded-xl shadow-md bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center border-2 border-red-500">
                        <div class="text-center">
                            <div class="text-white font-bold text-xs">ISA</div>
                            <div class="text-xs">👨‍💼</div>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800">Admin Dashboard</h1>
                        <p class="text-sm text-gray-600">Complete management system</p>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <a href="./quick-admin.html" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 text-sm">
                        Create Survey
                    </a>
                    <a href="./admin-stats.html" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 text-sm">
                        Statistics
                    </a>
                    <a href="./chef-dashboard.html" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 text-sm">
                        👨‍🍳 Chef Dashboard
                    </a>
                    <a href="./vote-generator.html" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 text-sm">
                        🗳️ Generate Votes
                    </a>
                    <a href="./system-settings.html" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 text-sm">
                        ⚙️ Settings
                    </a>
                    <a href="https://isa-meal-vote.web.app" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm">
                        View App
                    </a>
                    <button onclick="testConnection()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 text-sm">
                        🔍 Test
                    </button>
                    <button onclick="adminLogout()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 text-sm">
                        🔒 Logout
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-6 py-8">
        <!-- Tab Navigation -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6">
                    <button onclick="showTab('overview')" id="tab-overview" class="py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                        📊 Overview
                    </button>
                    <button onclick="showTab('users')" id="tab-users" class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                        👥 User Management
                    </button>
                    <button onclick="showTab('emails')" id="tab-emails" class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                        📧 Email Management
                    </button>
                    <button onclick="showTab('settings')" id="tab-settings" class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                        ⚙️ Settings
                    </button>
                </nav>
            </div>
        </div>

        <!-- Overview Tab -->
        <div id="content-overview" class="tab-content">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <!-- Quick Stats Cards -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <span class="text-xl">👥</span>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Users</p>
                            <p class="text-2xl font-semibold text-gray-900" id="totalUsers">0</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <span class="text-xl">📊</span>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Active Surveys</p>
                            <p class="text-2xl font-semibold text-gray-900" id="activeSurveys">0</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                            <span class="text-xl">✅</span>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Today's Participants</p>
                            <p class="text-2xl font-semibold text-gray-900" id="todayParticipants">0</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Sync -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Database Management</h3>
                    <div class="space-x-2">
                        <button onclick="testFirebaseConnection()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                            🔍 Test Connection
                        </button>
                        <button onclick="syncAllData()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                            🔄 Sync All Data
                        </button>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600" id="dbUsers">-</div>
                        <div class="text-sm text-blue-700">Users in Database</div>
                    </div>
                    <div class="text-center p-4 bg-green-50 rounded-lg">
                        <div class="text-2xl font-bold text-green-600" id="dbAuthorized">-</div>
                        <div class="text-sm text-green-700">Authorized Emails</div>
                    </div>
                    <div class="text-center p-4 bg-purple-50 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600" id="dbVotes">-</div>
                        <div class="text-sm text-purple-700">Total Votes</div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h3>
                <div id="recentActivity" class="space-y-3">
                    <div class="text-gray-500 text-center py-4">Loading recent activity...</div>
                </div>
            </div>
        </div>

        <!-- User Management Tab -->
        <div id="content-users" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-800">Registered Users</h3>
                        <button onclick="refreshUsers()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                            🔄 Refresh
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Vote</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="usersTable" class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td colspan="4" class="px-6 py-4 text-center text-gray-500">Loading users...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Management Tab -->
        <div id="content-emails" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Add Email -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Add Authorized Email</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                            <input type="email" id="newEmail" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="<EMAIL>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                            <select id="emailRole" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="student">Student</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                        <button onclick="addAuthorizedEmail()" class="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700">
                            ➕ Add Email
                        </button>
                    </div>
                </div>

                <!-- Authorized Emails List -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">Authorized Emails</h3>
                        <button onclick="refreshAuthorizedEmails()" class="text-blue-600 hover:text-blue-800">
                            🔄 Refresh
                        </button>
                    </div>
                    <div id="authorizedEmailsList" class="space-y-2 max-h-96 overflow-y-auto">
                        <div class="text-gray-500 text-center py-4">Loading authorized emails...</div>
                    </div>
                </div>
            </div>

            <!-- Bulk Actions -->
            <div class="bg-white rounded-lg shadow p-6 mt-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Bulk Actions</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Import from Database</label>
                        <button onclick="importUsersFromDB()" class="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700">
                            🔄 Import Users
                        </button>
                        <p class="text-xs text-gray-500 mt-1">Import all users who have voted</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Import Emails (CSV)</label>
                        <input type="file" id="csvFile" accept=".csv" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                        <button onclick="importEmails()" class="w-full mt-2 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700">
                            📤 Import CSV
                        </button>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Export Data</label>
                        <button onclick="exportEmails()" class="w-full bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700">
                            📥 Export Emails
                        </button>
                        <button onclick="exportVotes()" class="w-full mt-2 bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700">
                            📥 Export Votes
                        </button>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Danger Zone</label>
                        <button onclick="clearAllEmails()" class="w-full bg-red-600 text-white py-2 rounded-lg hover:bg-red-700">
                            🗑️ Clear All Emails
                        </button>
                        <button onclick="resetAllVotes()" class="w-full mt-2 bg-red-600 text-white py-2 rounded-lg hover:bg-red-700">
                            🗑️ Reset All Votes
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div id="content-settings" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- System Settings -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">System Settings</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Total Students</label>
                            <input type="number" id="totalStudentsSetting" class="w-full px-3 py-2 border border-gray-300 rounded-lg" value="100">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Voting Deadline Time</label>
                            <input type="time" id="votingDeadlineTime" class="w-full px-3 py-2 border border-gray-300 rounded-lg" value="13:30">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Participation Deadline Time</label>
                            <input type="time" id="participationDeadlineTime" class="w-full px-3 py-2 border border-gray-300 rounded-lg" value="23:59">
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="emailRestriction" class="mr-2">
                            <label class="text-sm text-gray-700">Restrict to authorized emails only</label>
                        </div>
                        <button onclick="saveSettings()" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700">
                            💾 Save Settings
                        </button>
                    </div>
                </div>

                <!-- Notifications -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">📢 Notification System</h3>

                    <!-- Notification Stats -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        <div class="bg-blue-50 p-3 rounded-lg text-center">
                            <div class="text-xl font-bold text-blue-600" id="notifTotalUsers">0</div>
                            <div class="text-xs text-blue-700">Total Users</div>
                        </div>
                        <div class="bg-green-50 p-3 rounded-lg text-center">
                            <div class="text-xl font-bold text-green-600" id="notifVotedUsers">0</div>
                            <div class="text-xs text-green-700">Voted</div>
                        </div>
                        <div class="bg-red-50 p-3 rounded-lg text-center">
                            <div class="text-xl font-bold text-red-600" id="notifNotVotedUsers">0</div>
                            <div class="text-xs text-red-700">Not Voted</div>
                        </div>
                        <div class="bg-purple-50 p-3 rounded-lg text-center">
                            <div class="text-xl font-bold text-purple-600" id="notifParticipants">0</div>
                            <div class="text-xs text-purple-700">Will Eat</div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <button onclick="sendVotingReminder()" class="bg-orange-600 text-white py-3 rounded-lg hover:bg-orange-700 font-semibold">
                            ⏰ Remind Non-Voters
                        </button>
                        <button onclick="sendParticipationReminder()" class="bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 font-semibold">
                            🍽️ Remind Undecided
                        </button>
                        <button onclick="sendResultsAnnouncement()" class="bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 font-semibold">
                            🎉 Announce Results
                        </button>
                    </div>

                    <!-- Custom Notification -->
                    <div class="border-t pt-4 space-y-4">
                        <h4 class="font-semibold text-gray-800">📝 Custom Notification</h4>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Message Title</label>
                                    <input type="text" id="notificationTitle" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm" placeholder="Notification title...">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Message Content</label>
                                    <textarea id="notificationMessage" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm" rows="3" placeholder="Your message..."></textarea>
                                </div>

                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Send To</label>
                                        <select id="notificationTarget" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm" onchange="updateNotificationPreview()">
                                            <option value="all">📧 All Users</option>
                                            <option value="not-voted">❌ Haven't Voted</option>
                                            <option value="voted">✅ Voted Today</option>
                                            <option value="participants">🍽️ Will Participate</option>
                                            <option value="declined">🚫 Won't Participate</option>
                                            <option value="undecided">⏳ Undecided</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Method</label>
                                        <select id="notificationType" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                            <option value="email">📧 Email</option>
                                            <option value="sms">📱 SMS</option>
                                            <option value="push">🔔 Push</option>
                                        </select>
                                    </div>
                                </div>

                                <button onclick="sendCustomNotification()" class="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 font-semibold">
                                    📤 Send Notification
                                </button>
                            </div>

                            <div class="space-y-3">
                                <!-- Preview -->
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <h5 class="font-semibold text-gray-800 mb-2 text-sm">📱 Preview</h5>
                                    <div class="bg-white rounded-lg p-3 border text-sm">
                                        <div class="font-semibold text-gray-800" id="previewTitle">Notification Title</div>
                                        <div class="text-gray-600 mt-1" id="previewContent">Your message will appear here...</div>
                                        <div class="text-xs text-gray-400 mt-2">ISA Meal Vote System</div>
                                    </div>
                                </div>

                                <!-- Recipients Info -->
                                <div class="bg-blue-50 rounded-lg p-3">
                                    <h5 class="font-semibold text-blue-800 mb-2 text-sm">👥 Recipients</h5>
                                    <div class="text-sm text-blue-700">
                                        <div>Target: <span id="recipientTarget">All Users</span></div>
                                        <div>Count: <span id="recipientCount">0</span> users</div>
                                        <div>Method: <span id="recipientMethod">Email</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Notifications -->
                    <div class="border-t pt-4 mt-4">
                        <div class="flex justify-between items-center mb-3">
                            <h4 class="font-semibold text-gray-800 text-sm">📋 Recent Notifications</h4>
                            <button onclick="loadRecentNotifications()" class="text-blue-600 hover:text-blue-800 text-xs">
                                🔄 Refresh
                            </button>
                        </div>
                        <div id="recentNotifications" class="space-y-2 max-h-40 overflow-y-auto">
                            <div class="text-gray-500 text-center py-2 text-sm">Loading...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Messages -->
        <div id="statusMessage" class="fixed bottom-4 right-4 max-w-sm"></div>
    </div>

    <script>
        // Simple inline script to ensure functions work
        function adminLogout() {
            console.log('Logout clicked');
            if (confirm('Are you sure you want to logout?')) {
                sessionStorage.removeItem('adminAuthenticated');
                alert('Logging out...');
                location.reload();
            }
        }

        function showTab(tabName) {
            console.log('Switching to tab:', tabName);
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(el => el.classList.add('hidden'));
            // Show selected tab
            document.getElementById('content-' + tabName).classList.remove('hidden');

            // Update tab buttons
            document.querySelectorAll('[id^="tab-"]').forEach(tab => {
                tab.classList.remove('border-blue-500', 'text-blue-600');
                tab.classList.add('border-transparent', 'text-gray-500');
            });
            document.getElementById('tab-' + tabName).classList.remove('border-transparent', 'text-gray-500');
            document.getElementById('tab-' + tabName).classList.add('border-blue-500', 'text-blue-600');
        }

        function testConnection() {
            console.log('Testing Firebase connection...');
            alert('Testing Firebase connection - check console for details');

            if (typeof firebase === 'undefined') {
                console.error('Firebase not loaded');
                alert('Firebase not loaded!');
                return;
            }

            console.log('Firebase loaded successfully');
            console.log('Config:', window.firebaseConfig);

            try {
                const db = firebase.firestore();
                db.collection('surveys').limit(1).get().then(snapshot => {
                    console.log('Firebase connection successful, surveys found:', snapshot.size);
                    alert('Firebase connection successful! Found ' + snapshot.size + ' surveys');
                }).catch(error => {
                    console.error('Firebase connection failed:', error);
                    alert('Firebase connection failed: ' + error.message);
                });
            } catch (error) {
                console.error('Error initializing Firebase:', error);
                alert('Error initializing Firebase: ' + error.message);
            }
        }

        // User management functions
        function refreshUsers() {
            console.log('Refreshing users...');
            if (typeof loadUsers === 'function') {
                loadUsers();
            } else {
                alert('loadUsers function not available');
            }
        }

        function viewUserDetails(userId) {
            alert('Viewing details for user: ' + userId + '\n\nThis feature will show user voting history.');
        }

        function removeUser(userId) {
            if (confirm('Are you sure you want to remove user: ' + userId + '?')) {
                alert('Remove user feature - would remove: ' + userId);
            }
        }

        // Email management functions
        async function addAuthorizedEmail() {
            const email = document.getElementById('newEmail').value.trim();
            const role = document.getElementById('emailRole').value;

            if (!email) {
                alert('Please enter an email address');
                return;
            }

            if (!email.includes('@')) {
                alert('Please enter a valid email address');
                return;
            }

            try {
                const db = firebase.firestore();

                // Check if email already exists
                const existingEmail = await db.collection('authorizedEmails')
                    .where('email', '==', email.toLowerCase()).get();

                if (!existingEmail.empty) {
                    alert('❌ Email already exists in authorized list');
                    return;
                }

                // Add new email
                await db.collection('authorizedEmails').add({
                    email: email.toLowerCase(),
                    role: role,
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    createdBy: 'admin'
                });

                // Clear form
                document.getElementById('newEmail').value = '';
                document.getElementById('emailRole').value = 'student';

                alert('✅ Email added successfully: ' + email);

                // Refresh the list
                if (typeof loadAuthorizedEmails === 'function') {
                    loadAuthorizedEmails();
                }

            } catch (error) {
                console.error('Error adding email:', error);
                alert('❌ Error adding email: ' + error.message);
            }
        }

        function refreshAuthorizedEmails() {
            console.log('Refreshing authorized emails...');
            if (typeof loadAuthorizedEmails === 'function') {
                loadAuthorizedEmails();
            } else {
                alert('loadAuthorizedEmails function not available');
            }
        }

        // Import users from database
        async function importUsersFromDB() {
            try {
                console.log('Starting user import from database...');

                if (typeof firebase === 'undefined' || !firebase.firestore) {
                    alert('Firebase not initialized');
                    return;
                }

                const db = firebase.firestore();

                // Get all unique users from votes
                const uniqueUsers = new Set();
                const surveysSnap = await db.collection('surveys').get();

                console.log('Found surveys:', surveysSnap.size);

                for (const surveyDoc of surveysSnap.docs) {
                    const votesSnap = await db.collection('surveys').doc(surveyDoc.id).collection('votes').get();
                    console.log(`Survey ${surveyDoc.id} has ${votesSnap.size} votes`);

                    votesSnap.docs.forEach(voteDoc => {
                        const voteData = voteDoc.data();
                        const userId = voteDoc.id;

                        // Try to get email from vote data
                        let email = voteData.userEmail || userId;
                        if (email.includes('@')) {
                            uniqueUsers.add(email.toLowerCase());
                        }
                    });
                }

                console.log('Unique users found:', uniqueUsers.size);

                if (uniqueUsers.size === 0) {
                    alert('No users with valid emails found in database');
                    return;
                }

                let imported = 0;
                const batch = db.batch();

                for (const email of uniqueUsers) {
                    // Check if email already exists
                    const existingEmail = await db.collection('authorizedEmails')
                        .where('email', '==', email).get();

                    if (existingEmail.empty) {
                        const docRef = db.collection('authorizedEmails').doc();
                        batch.set(docRef, {
                            email: email,
                            role: 'student',
                            createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                            createdBy: 'auto-import',
                            source: 'database'
                        });
                        imported++;
                    }
                }

                if (imported > 0) {
                    await batch.commit();
                    alert(`✅ Successfully imported ${imported} users from database!`);
                } else {
                    alert('ℹ️ All users are already in the authorized emails list');
                }

                // Refresh the emails list
                if (typeof loadAuthorizedEmails === 'function') {
                    loadAuthorizedEmails();
                }

            } catch (error) {
                console.error('Error importing users:', error);
                alert('❌ Error importing users: ' + error.message);
            }
        }

        // Remove authorized email
        async function removeAuthorizedEmail(docId) {
            if (!confirm('Are you sure you want to remove this email?')) {
                return;
            }

            try {
                const db = firebase.firestore();
                await db.collection('authorizedEmails').doc(docId).delete();
                alert('✅ Email removed successfully');

                // Refresh the list
                if (typeof loadAuthorizedEmails === 'function') {
                    loadAuthorizedEmails();
                }
            } catch (error) {
                console.error('Error removing email:', error);
                alert('❌ Error removing email: ' + error.message);
            }
        }

        // Export functions
        async function exportEmails() {
            try {
                const db = firebase.firestore();
                const emailsSnap = await db.collection('authorizedEmails').get();

                if (emailsSnap.empty) {
                    alert('No emails to export');
                    return;
                }

                const csvContent = 'Email,Role,Created Date,Created By\n' +
                    emailsSnap.docs.map(doc => {
                        const data = doc.data();
                        const createdDate = data.createdAt ? data.createdAt.toDate().toLocaleDateString() : 'Unknown';
                        return `${data.email},${data.role},${createdDate},${data.createdBy || 'Unknown'}`;
                    }).join('\n');

                // Download CSV
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', 'authorized-emails.csv');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                alert(`✅ Exported ${emailsSnap.size} emails`);

            } catch (error) {
                console.error('Error exporting emails:', error);
                alert('❌ Error exporting emails: ' + error.message);
            }
        }

        async function exportVotes() {
            try {
                const db = firebase.firestore();
                const surveysSnap = await db.collection('surveys').get();
                let allVotes = [];

                for (const surveyDoc of surveysSnap.docs) {
                    const surveyData = surveyDoc.data();
                    const votesSnap = await db.collection('surveys').doc(surveyDoc.id).collection('votes').get();

                    votesSnap.docs.forEach(voteDoc => {
                        const voteData = voteDoc.data();
                        const selectedOption = surveyData.options?.find(opt => opt.id === voteData.optionId);

                        allVotes.push({
                            survey: surveyData.title || surveyDoc.id,
                            email: voteData.userEmail || voteDoc.id,
                            selectedOption: selectedOption?.text || 'Unknown',
                            participate: voteData.participate,
                            voteDate: voteData.createdAt ? voteData.createdAt.toDate().toLocaleDateString() : 'Unknown',
                            voteTime: voteData.createdAt ? voteData.createdAt.toDate().toLocaleTimeString() : 'Unknown'
                        });
                    });
                }

                if (allVotes.length === 0) {
                    alert('No votes to export');
                    return;
                }

                const csvContent = 'Survey,Email,Selected Option,Will Participate,Vote Date,Vote Time\n' +
                    allVotes.map(vote =>
                        `"${vote.survey}","${vote.email}","${vote.selectedOption}",${vote.participate},${vote.voteDate},${vote.voteTime}`
                    ).join('\n');

                // Download CSV
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', 'all-votes.csv');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                alert(`✅ Exported ${allVotes.length} votes`);

            } catch (error) {
                console.error('Error exporting votes:', error);
                alert('❌ Error exporting votes: ' + error.message);
            }
        }

        // Notification functions
        async function sendVotingReminder() {
            try {
                const message = {
                    title: "⏰ Voting Reminder - Don't Miss Out!",
                    content: `Hi! The voting for tomorrow's lunch is still open until 13:30. Please cast your vote now to help us prepare the right amount of food and reduce waste. Vote at: ${window.location.origin}`,
                    target: 'not-voted',
                    type: 'voting-reminder'
                };

                // Send database notification
                await sendNotificationToDatabase(message);

                // Send SMS notifications
                const smsResult = await sendSMSNotifications('not-voted', 'voteReminder', {
                    appUrl: window.location.origin
                });

                alert(`✅ Voting reminder sent!\n📧 Database: Saved\n📱 SMS: ${smsResult.totalSent} sent, ${smsResult.totalFailed} failed`);

            } catch (error) {
                console.error('Error sending voting reminder:', error);
                alert('❌ Error sending voting reminder: ' + error.message);
            }
        }

        async function sendParticipationReminder() {
            try {
                const message = {
                    title: "🍽️ Please Confirm Your Participation",
                    content: "You voted for tomorrow's lunch! Please confirm if you will actually eat the meal to help us prepare the right portions and reduce food waste.",
                    target: 'undecided',
                    type: 'participation-reminder'
                };

                await sendNotificationToDatabase(message);
                alert('✅ Participation reminder sent to undecided users!');

            } catch (error) {
                console.error('Error sending participation reminder:', error);
                alert('❌ Error sending participation reminder: ' + error.message);
            }
        }

        async function sendCustomNotification() {
            const message = document.getElementById('notificationMessage').value.trim();
            const target = document.getElementById('notificationTarget').value;
            const type = document.getElementById('notificationType').value;

            if (!message) {
                alert('Please enter a notification message');
                return;
            }

            try {
                const notification = {
                    title: 'Custom Notification',
                    content: message,
                    target: target,
                    method: type,
                    type: 'custom'
                };

                await sendNotificationToDatabase(notification);

                // Get recipient count
                const count = await getRecipientCount(target);
                alert(`✅ Notification sent successfully!\n\nMessage: ${message}\nRecipients: ${count} users\nTarget: ${target}`);

                // Clear form
                document.getElementById('notificationMessage').value = '';

            } catch (error) {
                console.error('Error sending custom notification:', error);
                alert('❌ Error sending notification: ' + error.message);
            }
        }

        async function sendNotificationToDatabase(notification) {
            const db = firebase.firestore();
            const recipientCount = await getRecipientCount(notification.target);

            await db.collection('notifications').add({
                ...notification,
                sentAt: firebase.firestore.FieldValue.serverTimestamp(),
                sentBy: 'admin',
                recipientCount: recipientCount,
                status: 'sent'
            });
        }

        async function getRecipientCount(target) {
            try {
                const db = firebase.firestore();

                // Get user profiles with notification preferences
                const profilesSnap = await db.collection('userProfiles').get();
                const userProfiles = new Map();
                profilesSnap.docs.forEach(doc => {
                    userProfiles.set(doc.id, doc.data());
                });

                // Get authorized emails
                const emailsSnap = await db.collection('authorizedEmails').get();
                const totalUsers = emailsSnap.size;

                if (target === 'all') {
                    return totalUsers;
                }

                // Get current survey votes
                const surveysSnap = await db.collection('surveys').orderBy('createdAt', 'desc').limit(1).get();
                if (surveysSnap.empty) {
                    return target === 'not-voted' ? totalUsers : 0;
                }

                const currentSurvey = surveysSnap.docs[0];
                const votesSnap = await db.collection('surveys').doc(currentSurvey.id).collection('votes').get();

                const votedUsers = votesSnap.size;
                let participants = 0;
                let declined = 0;
                let undecided = 0;

                votesSnap.docs.forEach(doc => {
                    const voteData = doc.data();
                    if (voteData.participate === true) participants++;
                    else if (voteData.participate === false) declined++;
                    else undecided++;
                });

                switch(target) {
                    case 'voted': return votedUsers;
                    case 'not-voted': return totalUsers - votedUsers;
                    case 'participants': return participants;
                    case 'declined': return declined;
                    case 'undecided': return undecided;
                    default: return 0;
                }

            } catch (error) {
                console.error('Error getting recipient count:', error);
                return 0;
            }
        }

        // Load notification stats
        async function loadNotificationStats() {
            try {
                const db = firebase.firestore();

                // Get authorized emails
                const emailsSnap = await db.collection('authorizedEmails').get();
                const totalUsers = emailsSnap.size;

                // Get current survey votes
                const surveysSnap = await db.collection('surveys').orderBy('createdAt', 'desc').limit(1).get();

                let votedUsers = 0;
                let participants = 0;

                if (!surveysSnap.empty) {
                    const currentSurvey = surveysSnap.docs[0];
                    const votesSnap = await db.collection('surveys').doc(currentSurvey.id).collection('votes').get();

                    votedUsers = votesSnap.size;
                    participants = votesSnap.docs.filter(doc => doc.data().participate === true).length;
                }

                // Update notification stats
                document.getElementById('notifTotalUsers').textContent = totalUsers;
                document.getElementById('notifVotedUsers').textContent = votedUsers;
                document.getElementById('notifNotVotedUsers').textContent = totalUsers - votedUsers;
                document.getElementById('notifParticipants').textContent = participants;

            } catch (error) {
                console.error('Error loading notification stats:', error);
            }
        }

        // Update notification preview
        function updateNotificationPreview() {
            const title = document.getElementById('notificationTitle').value || 'Notification Title';
            const content = document.getElementById('notificationMessage').value || 'Your message will appear here...';
            const target = document.getElementById('notificationTarget').value;
            const method = document.getElementById('notificationType').value;

            document.getElementById('previewTitle').textContent = title;
            document.getElementById('previewContent').textContent = content;

            // Update recipient info
            const targetNames = {
                'all': 'All Users',
                'not-voted': 'Haven\'t Voted',
                'voted': 'Voted Today',
                'participants': 'Will Participate',
                'declined': 'Won\'t Participate',
                'undecided': 'Undecided'
            };

            document.getElementById('recipientTarget').textContent = targetNames[target] || target;
            document.getElementById('recipientMethod').textContent = method.charAt(0).toUpperCase() + method.slice(1);

            // Update count (simplified)
            getRecipientCount(target).then(count => {
                document.getElementById('recipientCount').textContent = count;
            });
        }

        // Load recent notifications
        async function loadRecentNotifications() {
            try {
                const db = firebase.firestore();
                const notificationsSnap = await db.collection('notifications')
                    .orderBy('sentAt', 'desc')
                    .limit(5)
                    .get();

                const container = document.getElementById('recentNotifications');

                if (notificationsSnap.empty) {
                    container.innerHTML = '<div class="text-gray-500 text-center py-2 text-sm">No notifications sent yet</div>';
                    return;
                }

                container.innerHTML = notificationsSnap.docs.map(doc => {
                    const data = doc.data();
                    const sentAt = data.sentAt ? data.sentAt.toDate().toLocaleString() : 'Unknown';

                    return `
                        <div class="bg-gray-50 rounded p-2 text-sm">
                            <div class="font-semibold text-gray-800">${data.title}</div>
                            <div class="text-gray-600 text-xs mt-1">${data.target} • ${data.recipientCount} users • ${sentAt}</div>
                        </div>
                    `;
                }).join('');

            } catch (error) {
                console.error('Error loading recent notifications:', error);
            }
        }

        // Send results announcement
        async function sendResultsAnnouncement() {
            try {
                // Get current survey and winning option
                const db = firebase.firestore();
                const surveysSnap = await db.collection('surveys').orderBy('createdAt', 'desc').limit(1).get();

                if (surveysSnap.empty) {
                    alert('No active survey found');
                    return;
                }

                const survey = surveysSnap.docs[0].data();
                const winningMeal = survey.winningOption ? survey.winningOption.text : 'TBD';

                const message = {
                    title: "🎉 Tomorrow's Lunch Menu Announced!",
                    content: `Great news! Tomorrow's lunch will be: ${winningMeal}. Thanks to everyone who voted! See you at lunch time.`,
                    target: 'all',
                    type: 'results-announcement'
                };

                await sendNotificationToDatabase(message);
                alert('✅ Results announcement sent to all users!');
                loadRecentNotifications();

            } catch (error) {
                console.error('Error sending results:', error);
                alert('❌ Error sending results announcement: ' + error.message);
            }
        }

        // Add event listeners for preview updates
        document.addEventListener('DOMContentLoaded', function() {
            // Load notification stats when settings tab is opened
            const settingsTab = document.getElementById('tab-settings');
            if (settingsTab) {
                settingsTab.addEventListener('click', function() {
                    setTimeout(loadNotificationStats, 100);
                    setTimeout(loadRecentNotifications, 100);
                });
            }

            // Add event listeners for preview updates
            const titleInput = document.getElementById('notificationTitle');
            const contentInput = document.getElementById('notificationMessage');
            const targetSelect = document.getElementById('notificationTarget');
            const typeSelect = document.getElementById('notificationType');

            if (titleInput) titleInput.addEventListener('input', updateNotificationPreview);
            if (contentInput) contentInput.addEventListener('input', updateNotificationPreview);
            if (targetSelect) targetSelect.addEventListener('change', updateNotificationPreview);
            if (typeSelect) typeSelect.addEventListener('change', updateNotificationPreview);
        });

        // SMS Notification Functions
        async function sendSMSNotifications(target, notificationType, data = {}) {
            try {
                const db = firebase.firestore();

                // Get user profiles with notification preferences
                const profilesSnap = await db.collection('userProfiles').get();
                const userProfiles = [];

                profilesSnap.docs.forEach(doc => {
                    const profile = doc.data();
                    if (profile.phoneNumber && profile.notifications?.sms && profile.notifications?.enabled) {
                        userProfiles.push({
                            userId: doc.id,
                            email: profile.email,
                            phoneNumber: profile.phoneNumber,
                            notifications: profile.notifications
                        });
                    }
                });

                // Filter users based on target
                let targetUsers = userProfiles;

                if (target !== 'all') {
                    // Get current survey and votes to filter users
                    const surveysSnap = await db.collection('surveys').orderBy('createdAt', 'desc').limit(1).get();

                    if (!surveysSnap.empty) {
                        const currentSurvey = surveysSnap.docs[0];
                        const votesSnap = await db.collection('surveys').doc(currentSurvey.id).collection('votes').get();

                        const votedUserIds = new Set();
                        const participantUserIds = new Set();
                        const declinedUserIds = new Set();
                        const undecidedUserIds = new Set();

                        votesSnap.docs.forEach(voteDoc => {
                            const voteData = voteDoc.data();
                            const userId = voteDoc.id;

                            votedUserIds.add(userId);

                            if (voteData.participate === true) {
                                participantUserIds.add(userId);
                            } else if (voteData.participate === false) {
                                declinedUserIds.add(userId);
                            } else {
                                undecidedUserIds.add(userId);
                            }
                        });

                        // Filter based on target
                        switch(target) {
                            case 'not-voted':
                                targetUsers = userProfiles.filter(user => !votedUserIds.has(user.userId));
                                break;
                            case 'voted':
                                targetUsers = userProfiles.filter(user => votedUserIds.has(user.userId));
                                break;
                            case 'participants':
                                targetUsers = userProfiles.filter(user => participantUserIds.has(user.userId));
                                break;
                            case 'declined':
                                targetUsers = userProfiles.filter(user => declinedUserIds.has(user.userId));
                                break;
                            case 'undecided':
                                targetUsers = userProfiles.filter(user => undecidedUserIds.has(user.userId));
                                break;
                        }
                    }
                }

                // Filter users who have the specific notification type enabled
                targetUsers = targetUsers.filter(user =>
                    user.notifications[notificationType] !== false
                );

                // Send SMS using the SMS service
                if (window.SMSService && targetUsers.length > 0) {
                    return await window.SMSService.sendNotification(targetUsers, notificationType, data);
                } else {
                    return {
                        success: true,
                        totalSent: 0,
                        totalFailed: 0,
                        message: 'No SMS recipients found or SMS service not available'
                    };
                }

            } catch (error) {
                console.error('Error sending SMS notifications:', error);
                return {
                    success: false,
                    totalSent: 0,
                    totalFailed: 0,
                    error: error.message
                };
            }
        }
    </script>
    <script src="./admin-dashboard.js"></script>
</body>
</html>
