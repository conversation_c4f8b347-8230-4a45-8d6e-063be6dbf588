<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vote Generator - ISA Meal Vote</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-firestore-compat.js"></script>
    <script src="./firebase-config.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-6 py-8">
        <div class="bg-white rounded-xl shadow-xl p-8 max-w-2xl mx-auto">
            <div class="text-center mb-8">
                <div class="w-16 h-16 mx-auto mb-4 rounded-xl shadow-md bg-gradient-to-br from-purple-600 to-purple-700 flex items-center justify-center">
                    <span class="text-white text-2xl">🗳️</span>
                </div>
                <h1 class="text-3xl font-bold text-gray-800">Vote Generator</h1>
                <p class="text-gray-600 mt-2">Generate test votes for surveys</p>
            </div>

            <!-- Survey Selection -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Select Survey</label>
                <select id="surveySelect" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    <option value="">Loading surveys...</option>
                </select>
            </div>

            <!-- Vote Configuration -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Number of Votes</label>
                    <input type="number" id="voteCount" value="24" min="1" max="200" 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email Domain</label>
                    <input type="text" id="emailDomain" value="student.isa.tn" placeholder="example.com"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                </div>
            </div>

            <!-- Participation Settings -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-3">Participation Distribution</label>
                <div class="grid grid-cols-3 gap-4">
                    <div>
                        <label class="block text-xs text-gray-600 mb-1">Will Participate (%)</label>
                        <input type="number" id="participatePercent" value="70" min="0" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                    </div>
                    <div>
                        <label class="block text-xs text-gray-600 mb-1">Won't Participate (%)</label>
                        <input type="number" id="declinePercent" value="20" min="0" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                    </div>
                    <div>
                        <label class="block text-xs text-gray-600 mb-1">Undecided (%)</label>
                        <input type="number" id="undecidedPercent" value="10" min="0" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="space-y-4">
                <button onclick="generateVotes()" class="w-full bg-purple-600 text-white py-4 rounded-lg hover:bg-purple-700 font-semibold text-lg">
                    🚀 Generate Votes
                </button>
                
                <div class="grid grid-cols-2 gap-4">
                    <button onclick="loadSurveys()" class="bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700">
                        🔄 Refresh Surveys
                    </button>
                    <button onclick="clearAllVotes()" class="bg-red-600 text-white py-2 rounded-lg hover:bg-red-700">
                        🗑️ Clear All Votes
                    </button>
                </div>
            </div>

            <!-- Progress -->
            <div id="progress" class="mt-6 hidden">
                <div class="bg-gray-200 rounded-full h-4">
                    <div id="progressBar" class="bg-purple-600 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <p id="progressText" class="text-center text-sm text-gray-600 mt-2">Generating votes...</p>
            </div>

            <!-- Results -->
            <div id="results" class="mt-6 space-y-3"></div>

            <!-- Navigation -->
            <div class="mt-8 text-center space-x-4">
                <a href="./admin-dashboard.html" class="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700">
                    👨‍💼 Admin Dashboard
                </a>
                <a href="./quick-admin.html" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700">
                    📊 Create Survey
                </a>
                <a href="https://isa-meal-vote.web.app" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                    🍽️ Vote App
                </a>
            </div>
        </div>
    </div>

    <script>
        // Initialize Firebase
        const app = firebase.initializeApp(window.firebaseConfig);
        const db = firebase.firestore();

        // Load surveys on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadSurveys();
        });

        async function loadSurveys() {
            try {
                const surveysSnap = await db.collection('surveys').get();
                const surveySelect = document.getElementById('surveySelect');
                
                if (surveysSnap.empty) {
                    surveySelect.innerHTML = '<option value="">No surveys found</option>';
                    return;
                }

                surveySelect.innerHTML = '<option value="">Select a survey...</option>';
                surveysSnap.docs.forEach(doc => {
                    const data = doc.data();
                    const option = document.createElement('option');
                    option.value = doc.id;
                    option.textContent = `${data.title || doc.id} (${data.phase || 'active'})`;
                    surveySelect.appendChild(option);
                });

                addResult('✅ Loaded ' + surveysSnap.size + ' surveys', 'success');
            } catch (error) {
                console.error('Error loading surveys:', error);
                addResult('❌ Error loading surveys: ' + error.message, 'error');
            }
        }

        async function generateVotes() {
            const surveyId = document.getElementById('surveySelect').value;
            const voteCount = parseInt(document.getElementById('voteCount').value);
            const emailDomain = document.getElementById('emailDomain').value;
            const participatePercent = parseInt(document.getElementById('participatePercent').value);
            const declinePercent = parseInt(document.getElementById('declinePercent').value);
            const undecidedPercent = parseInt(document.getElementById('undecidedPercent').value);

            if (!surveyId) {
                addResult('❌ Please select a survey', 'error');
                return;
            }

            if (participatePercent + declinePercent + undecidedPercent !== 100) {
                addResult('❌ Participation percentages must add up to 100%', 'error');
                return;
            }

            try {
                // Get survey data
                const surveyDoc = await db.collection('surveys').doc(surveyId).get();
                if (!surveyDoc.exists) {
                    addResult('❌ Survey not found', 'error');
                    return;
                }

                const surveyData = surveyDoc.data();
                const options = surveyData.options || [];

                if (options.length === 0) {
                    addResult('❌ Survey has no options', 'error');
                    return;
                }

                // Show progress
                const progressDiv = document.getElementById('progress');
                const progressBar = document.getElementById('progressBar');
                const progressText = document.getElementById('progressText');
                progressDiv.classList.remove('hidden');

                // Clear previous results
                document.getElementById('results').innerHTML = '';

                let generated = 0;
                const batch = db.batch();

                for (let i = 0; i < voteCount; i++) {
                    // Generate user data
                    const userId = generateUserId();
                    const userEmail = `student${i + 1}@${emailDomain}`;
                    
                    // Random option selection
                    const randomOption = options[Math.floor(Math.random() * options.length)];
                    
                    // Determine participation based on percentages
                    const rand = Math.random() * 100;
                    let participate;
                    if (rand < participatePercent) {
                        participate = true;
                    } else if (rand < participatePercent + declinePercent) {
                        participate = false;
                    } else {
                        participate = null; // undecided
                    }

                    // Create vote document
                    const voteRef = db.collection('surveys').doc(surveyId).collection('votes').doc(userId);
                    batch.set(voteRef, {
                        optionId: randomOption.id,
                        participate: participate,
                        userEmail: userEmail,
                        userId: userId,
                        createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                        generatedVote: true // Mark as generated for easy identification
                    });

                    generated++;

                    // Update progress
                    const progress = (generated / voteCount) * 100;
                    progressBar.style.width = progress + '%';
                    progressText.textContent = `Generated ${generated}/${voteCount} votes...`;

                    // Commit in batches of 50 to avoid limits
                    if (generated % 50 === 0 || generated === voteCount) {
                        await batch.commit();
                        console.log(`Committed batch: ${generated} votes`);
                    }
                }

                // Final commit
                await batch.commit();

                progressText.textContent = `✅ Successfully generated ${generated} votes!`;
                addResult(`🎉 Generated ${generated} votes for survey: ${surveyData.title}`, 'success');
                addResult(`📊 Distribution: ${Math.round(voteCount * participatePercent / 100)} will participate, ${Math.round(voteCount * declinePercent / 100)} won't, ${Math.round(voteCount * undecidedPercent / 100)} undecided`, 'info');

            } catch (error) {
                console.error('Error generating votes:', error);
                addResult('❌ Error generating votes: ' + error.message, 'error');
            }
        }

        async function clearAllVotes() {
            const surveyId = document.getElementById('surveySelect').value;
            
            if (!surveyId) {
                addResult('❌ Please select a survey', 'error');
                return;
            }

            if (!confirm('Are you sure you want to clear ALL votes for this survey? This cannot be undone!')) {
                return;
            }

            try {
                const votesSnap = await db.collection('surveys').doc(surveyId).collection('votes').get();
                const batch = db.batch();

                votesSnap.docs.forEach(doc => {
                    batch.delete(doc.ref);
                });

                await batch.commit();
                addResult(`🗑️ Cleared ${votesSnap.size} votes from survey`, 'success');

            } catch (error) {
                console.error('Error clearing votes:', error);
                addResult('❌ Error clearing votes: ' + error.message, 'error');
            }
        }

        function generateUserId() {
            return 'gen_' + Math.random().toString(36).substr(2, 16);
        }

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 
                           type === 'error' ? 'bg-red-50 border-red-200 text-red-800' : 
                           'bg-blue-50 border-blue-200 text-blue-800';
            
            div.className = `p-3 rounded-lg border ${bgColor}`;
            div.textContent = message;
            results.appendChild(div);
            
            // Auto-scroll to bottom
            results.scrollTop = results.scrollHeight;
        }
    </script>
</body>
</html>
