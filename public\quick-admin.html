<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Admin - No Login Required</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-firestore-compat.js"></script>
    <script src="./firebase-config.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-8 text-center text-red-600">⚠️ Quick Admin (No Auth)</h1>
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <p class="text-yellow-800"><strong>Warning:</strong> This is a temporary admin panel without authentication. Use only for testing!</p>
        </div>
        
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Create Tomorrow's Menu</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="p-3 bg-blue-50 rounded">
                    <label class="block">
                        <span class="font-semibold text-blue-800">Total Students:</span>
                        <input type="number" id="total-students" class="w-full p-2 border rounded mt-1" value="100" onchange="updateThresholdDisplay()">
                    </label>
                </div>
                <div class="p-3 bg-green-50 rounded">
                    <label class="block">
                        <span class="font-semibold text-green-800">Votes Required:</span>
                        <input type="number" id="vote-threshold" class="w-full p-2 border rounded mt-1" value="25" onchange="updateThresholdDisplay()">
                    </label>
                </div>
            </div>

            <div class="mb-4 p-3 bg-yellow-50 rounded border border-yellow-200">
                <div class="flex items-center justify-between mb-2">
                    <span class="font-semibold text-yellow-800">📊 Threshold Settings</span>
                    <button onclick="usePercentageThreshold()" class="text-xs bg-yellow-600 text-white px-3 py-1 rounded hover:bg-yellow-700">
                        Use 50% Auto
                    </button>
                </div>
                <p class="text-sm text-yellow-700">
                    <span id="threshold-display">Need 25 votes to confirm meal</span>
                </p>
                <p class="text-xs text-yellow-600 mt-1">
                    <span id="percentage-display">25% of total students</span>
                </p>
            </div>
            
            <button onclick="createTomorrowSurvey()" class="w-full bg-green-600 text-white p-3 rounded hover:bg-green-700 mb-4">
                Create Tomorrow's Menu Survey
            </button>
            
            <button onclick="clearAllSurveys()" class="w-full bg-red-600 text-white p-3 rounded hover:bg-red-700 mb-4">
                Clear All Surveys
            </button>

            <button onclick="checkAndUpdatePhases()" class="w-full bg-orange-600 text-white p-3 rounded hover:bg-orange-700 mb-4">
                🕐 Check Time & Update Phases
            </button>

            <div id="status" class="p-4 rounded bg-gray-50 min-h-20"></div>
            
            <div class="mt-6 text-center space-x-2">
                <a href="./system-settings.html" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm">
                    ⚙️ Settings
                </a>
                <a href="./admin-dashboard.html" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 text-sm">
                    👨‍💼 Dashboard
                </a>
                <a href="./vote-generator.html" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 text-sm">
                    🗳️ Generate Votes
                </a>
                <a href="./admin-stats.html" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 text-sm">
                    📊 Statistics
                </a>
                <a href="https://isa-meal-vote.web.app" target="_blank" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm">
                    View App
                </a>
            </div>
        </div>
    </div>

    <script>
        const app = firebase.initializeApp(window.firebaseConfig);
        const db = firebase.firestore();

        function updateStatus(message) {
            document.getElementById('status').innerHTML = `<p class="text-sm">${message}</p>`;
        }

        function getTomorrowDay() {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
            return days[tomorrow.getDay()];
        }

        function getTomorrowDate() {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            return tomorrow;
        }

        function getDayMenus(day) {
            const menus = {
                monday: [
                    { id: '1', text: 'Couscous with Lamb and Vegetables' },
                    { id: '2', text: 'Brik with Tuna and Egg' },
                    { id: '3', text: 'Makloubeh (Tunisian Rice with Chicken)' },
                    { id: '4', text: 'Chorba (Tunisian Soup) with Bread' },
                    { id: '5', text: 'Grilled Merguez with Salad' }
                ],
                tuesday: [
                    { id: '1', text: 'Tajine with Chicken and Olives' },
                    { id: '2', text: 'Lablabi (Chickpea Stew)' },
                    { id: '3', text: 'Kafteji (Tunisian Ratatouille)' },
                    { id: '4', text: 'Ojja with Merguez and Eggs' },
                    { id: '5', text: 'Tunisian Fish with Harissa' }
                ],
                wednesday: [
                    { id: '1', text: 'Mloukhia (Jute Leaves Stew) with Rice' },
                    { id: '2', text: 'Couscous with Fish and Vegetables' },
                    { id: '3', text: 'Tunisian Pasta with Spicy Sauce' },
                    { id: '4', text: 'Shakshuka Tunisian Style' },
                    { id: '5', text: 'Rechta (Tunisian Noodles) with Chicken' }
                ],
                thursday: [
                    { id: '1', text: 'Mechouia Salad with Grilled Meat' },
                    { id: '2', text: 'Tunisian Sandwich (Casse-croûte)' },
                    { id: '3', text: 'Bambalouni with Savory Filling' },
                    { id: '4', text: 'Kammounia (Beef with Cumin Stew)' },
                    { id: '5', text: 'Grilled Chicken with Tunisian Spices' }
                ],
                friday: [
                    { id: '1', text: 'Couscous Royal (Mixed Meat)' },
                    { id: '2', text: 'Brik with Chicken and Cheese' },
                    { id: '3', text: 'Tunisian Lamb Stew' },
                    { id: '4', text: 'Malfouf (Stuffed Cabbage Rolls)' },
                    { id: '5', text: 'Grilled Sea Bass with Chermoula' }
                ],
                saturday: [
                    { id: '1', text: 'Weekend Special Couscous' },
                    { id: '2', text: 'Mixed Grill Tunisian Style' },
                    { id: '3', text: 'Stuffed Peppers (Felfel Mahshi)' },
                    { id: '4', text: 'Tunisian Pizza (Pâte Brisée)' },
                    { id: '5', text: 'Lamb Tagine with Dried Fruits' }
                ],
                sunday: [
                    { id: '1', text: 'Sunday Couscous with Seven Vegetables' },
                    { id: '2', text: 'Roasted Chicken with Tunisian Herbs' },
                    { id: '3', text: 'Beef Bourguignon Tunisian Style' },
                    { id: '4', text: 'Seafood Paella Tunisian Version' },
                    { id: '5', text: 'Traditional Osban (Tunisian Sausage)' }
                ]
            };
            return menus[day] || menus.monday;
        }

        async function createTomorrowSurvey() {
            updateStatus('Creating tomorrow\'s menu survey...');
            
            const totalStudents = parseInt(document.getElementById('total-students').value) || 100;
            const customThreshold = parseInt(document.getElementById('vote-threshold').value);
            const threshold = customThreshold || Math.ceil(totalStudents * 0.5);
            
            const tomorrowDay = getTomorrowDay();
            const tomorrowDate = getTomorrowDate();
            const dayName = tomorrowDay.charAt(0).toUpperCase() + tomorrowDay.slice(1);
            
            // Clear existing surveys first
            await clearAllSurveys(false);
            
            // Set voting deadline to 13:30 tomorrow
            const votingDeadline = new Date(tomorrowDate);
            votingDeadline.setHours(13, 30, 0, 0);

            // Set participation deadline to midnight tomorrow
            const participationDeadline = new Date(tomorrowDate);
            participationDeadline.setHours(23, 59, 59, 999);

            const survey = {
                id: `${tomorrowDay}-lunch`,
                title: `${dayName}'s Lunch Menu`,
                description: `Vote until 13:30, then confirm participation until midnight.`,
                votingDeadline: votingDeadline,
                participationDeadline: participationDeadline,
                totalStudents: totalStudents,
                threshold: threshold,
                status: 'voting', // voting, results, participation, closed
                phase: 'voting', // voting, results, participation, closed
                createdAt: new Date(),
                options: getDayMenus(tomorrowDay),
                winningOption: null
            };

            try {
                await db.collection('surveys').doc(survey.id).set({
                    title: survey.title,
                    description: survey.description,
                    votingDeadline: firebase.firestore.Timestamp.fromDate(survey.votingDeadline),
                    participationDeadline: firebase.firestore.Timestamp.fromDate(survey.participationDeadline),
                    totalStudents: survey.totalStudents,
                    threshold: survey.threshold,
                    status: survey.status,
                    phase: survey.phase,
                    createdAt: firebase.firestore.Timestamp.fromDate(survey.createdAt),
                    options: survey.options,
                    winningOption: survey.winningOption
                });
                
                updateStatus(`🎉 Tomorrow's menu survey created!<br>
                    <strong>${survey.title}</strong><br>
                    5 delicious Tunisian options available<br>
                    <a href="https://isa-meal-vote.web.app" target="_blank" class="text-blue-600 underline">Visit your app now</a>`);
                
            } catch (error) {
                updateStatus(`❌ Error: ${error.message}`);
                console.error('Error creating survey:', error);
            }
        }

        async function clearAllSurveys(showStatus = true) {
            if (showStatus) updateStatus('Clearing all surveys...');
            
            try {
                const surveysSnap = await db.collection('surveys').get();
                const batch = db.batch();
                
                surveysSnap.docs.forEach(doc => {
                    batch.delete(doc.ref);
                });
                
                await batch.commit();
                
                if (showStatus) {
                    updateStatus('✅ All surveys cleared successfully!');
                }
                
            } catch (error) {
                if (showStatus) {
                    updateStatus(`❌ Error clearing surveys: ${error.message}`);
                }
                console.error('Error clearing surveys:', error);
            }
        }

        async function checkAndUpdatePhases() {
            updateStatus('Checking time and updating phases...');

            try {
                const now = new Date();
                const surveysSnap = await db.collection('surveys').get();

                if (surveysSnap.empty) {
                    updateStatus('❌ No surveys found to update');
                    return;
                }

                for (const doc of surveysSnap.docs) {
                    const survey = { id: doc.id, ...doc.data() };
                    const votingDeadline = survey.votingDeadline.toDate();
                    const participationDeadline = survey.participationDeadline.toDate();

                    let newPhase = survey.phase;
                    let winningOption = survey.winningOption;

                    // Check if voting period has ended
                    if (now > votingDeadline && survey.phase === 'voting') {
                        // Calculate winning option
                        const votesSnap = await db.collection('surveys').doc(survey.id).collection('votes').get();
                        const votes = votesSnap.docs.map(d => d.data());

                        const optionCounts = {};
                        survey.options.forEach(option => {
                            optionCounts[option.id] = { count: 0, text: option.text };
                        });

                        votes.forEach(vote => {
                            if (optionCounts[vote.optionId]) {
                                optionCounts[vote.optionId].count++;
                            }
                        });

                        // Find winning option (most votes)
                        let maxVotes = 0;
                        let winner = null;
                        Object.entries(optionCounts).forEach(([optionId, data]) => {
                            if (data.count > maxVotes) {
                                maxVotes = data.count;
                                winner = { id: optionId, text: data.text, votes: data.count };
                            }
                        });

                        winningOption = winner;
                        newPhase = 'results';
                    }

                    // Check if results period should end and participation begin
                    if (now > votingDeadline && now <= participationDeadline && survey.phase === 'results') {
                        // Keep in results phase until manually moved to participation
                        // or automatically after some time
                        const resultsEndTime = new Date(votingDeadline.getTime() + 30 * 60 * 1000); // 30 minutes after voting ends
                        if (now > resultsEndTime) {
                            newPhase = 'participation';
                        }
                    }

                    // Check if participation period has ended
                    if (now > participationDeadline && survey.phase !== 'closed') {
                        newPhase = 'closed';
                    }

                    // Update survey if phase changed
                    if (newPhase !== survey.phase || winningOption !== survey.winningOption) {
                        await db.collection('surveys').doc(survey.id).update({
                            phase: newPhase,
                            winningOption: winningOption,
                            updatedAt: firebase.firestore.FieldValue.serverTimestamp()
                        });

                        updateStatus(`✅ Updated ${survey.title}: ${survey.phase} → ${newPhase}`);
                    }
                }

                updateStatus('🎉 Phase check completed successfully!');

            } catch (error) {
                updateStatus(`❌ Error updating phases: ${error.message}`);
                console.error('Error updating phases:', error);
            }
        }

        // Threshold management functions
        function updateThresholdDisplay() {
            const totalStudents = parseInt(document.getElementById('total-students').value) || 100;
            const customThreshold = parseInt(document.getElementById('vote-threshold').value) || 25;
            const percentage = Math.round((customThreshold / totalStudents) * 100);

            document.getElementById('threshold-display').textContent = `Need ${customThreshold} votes to confirm meal`;
            document.getElementById('percentage-display').textContent = `${percentage}% of total students`;
        }

        function usePercentageThreshold() {
            const totalStudents = parseInt(document.getElementById('total-students').value) || 100;
            const fiftyPercentThreshold = Math.ceil(totalStudents * 0.5);

            document.getElementById('vote-threshold').value = fiftyPercentThreshold;
            updateThresholdDisplay();

            updateStatus(`✅ Set threshold to 50%: ${fiftyPercentThreshold} votes required`);
        }

        // Initialize display on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateThresholdDisplay();
        });
    </script>
</body>
</html>
