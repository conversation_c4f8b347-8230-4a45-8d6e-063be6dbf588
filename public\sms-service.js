// SMS Service for ISA Meal Vote Platform
// This is a simulated SMS service for demonstration purposes
// In production, integrate with a real SMS provider like Twilio, Nexmo, or local Tunisian SMS gateway

class SMSService {
    constructor() {
        this.apiKey = 'demo-key'; // In production, use environment variables
        this.baseUrl = 'https://api.sms-provider.tn'; // Example Tunisian SMS provider
        this.fromNumber = '+21670000000'; // Your SMS sender number
    }

    // Validate Tunisian phone number
    isValidTunisianPhone(phoneNumber) {
        // Remove country code if present
        const cleanNumber = phoneNumber.replace(/^\+216/, '');
        // Tunisian mobile numbers: 8 digits starting with 2, 4, 5, or 9
        const tunisianMobileRegex = /^[2459]\d{7}$/;
        return tunisianMobileRegex.test(cleanNumber);
    }

    // Format phone number for SMS sending
    formatPhoneNumber(phoneNumber) {
        // Ensure it starts with +216
        if (phoneNumber.startsWith('+216')) {
            return phoneNumber;
        }
        if (phoneNumber.startsWith('216')) {
            return '+' + phoneNumber;
        }
        return '+216' + phoneNumber;
    }

    // Send single SMS (simulated)
    async sendSMS(phoneNumber, message) {
        try {
            const formattedNumber = this.formatPhoneNumber(phoneNumber);
            
            if (!this.isValidTunisianPhone(formattedNumber)) {
                throw new Error('Invalid Tunisian phone number: ' + phoneNumber);
            }

            // Simulate SMS sending
            console.log('📱 SMS Sent (Simulated):');
            console.log('To:', formattedNumber);
            console.log('Message:', message);
            console.log('Timestamp:', new Date().toISOString());

            // In production, replace this with actual SMS API call:
            /*
            const response = await fetch(`${this.baseUrl}/send`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    to: formattedNumber,
                    from: this.fromNumber,
                    message: message
                })
            });

            if (!response.ok) {
                throw new Error('SMS sending failed');
            }

            return await response.json();
            */

            // Simulated successful response
            return {
                success: true,
                messageId: 'sim_' + Date.now(),
                to: formattedNumber,
                status: 'sent',
                cost: 0.05 // TND
            };

        } catch (error) {
            console.error('SMS sending error:', error);
            return {
                success: false,
                error: error.message,
                to: phoneNumber
            };
        }
    }

    // Send bulk SMS
    async sendBulkSMS(recipients, message) {
        const results = [];
        
        for (const recipient of recipients) {
            const result = await this.sendSMS(recipient.phoneNumber, message);
            results.push({
                ...result,
                userId: recipient.userId,
                email: recipient.email
            });
            
            // Add small delay to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        return {
            totalSent: results.filter(r => r.success).length,
            totalFailed: results.filter(r => !r.success).length,
            results: results
        };
    }

    // Get SMS templates for different notification types
    getTemplate(type, data = {}) {
        const templates = {
            voteReminder: `🍽️ ISA Meal Vote: Don't forget to vote for tomorrow's lunch! Voting closes at 13:30. Vote now: ${data.appUrl || 'https://isa-meal-vote.web.app'}`,
            
            participationReminder: `🍽️ ISA Meal Vote: You voted for tomorrow's lunch! Please confirm if you'll actually eat the meal to help reduce food waste. Confirm now: ${data.appUrl || 'https://isa-meal-vote.web.app'}`,
            
            resultAnnouncement: `🎉 ISA Meal Vote: Tomorrow's lunch will be "${data.winningMeal || 'TBD'}"! Thanks for voting. See you at lunch time! 🍽️`,
            
            mealReady: `🍽️ ISA Meal Vote: Your meal "${data.mealName || 'lunch'}" is ready for pickup! Please come to the cafeteria. Bon appétit! 😋`,
            
            lowParticipation: `⚠️ ISA Meal Vote: Low participation for tomorrow's lunch. We need more votes to confirm meal preparation. Vote now: ${data.appUrl || 'https://isa-meal-vote.web.app'}`,
            
            thresholdReached: `✅ ISA Meal Vote: Great news! We reached the minimum votes for tomorrow's lunch. Meal preparation confirmed! 🎉`,
            
            custom: data.message || 'ISA Meal Vote notification'
        };

        return templates[type] || templates.custom;
    }

    // Send notification based on type and user preferences
    async sendNotification(users, notificationType, data = {}) {
        const message = this.getTemplate(notificationType, data);
        
        // Filter users who have SMS enabled and valid phone numbers
        const smsRecipients = users.filter(user => {
            return user.phoneNumber && 
                   user.notifications?.sms && 
                   user.notifications?.enabled &&
                   user.notifications?.[notificationType] !== false;
        });

        if (smsRecipients.length === 0) {
            return {
                success: true,
                message: 'No SMS recipients found',
                totalSent: 0,
                totalFailed: 0
            };
        }

        console.log(`📱 Sending ${notificationType} SMS to ${smsRecipients.length} users`);
        
        const result = await this.sendBulkSMS(smsRecipients, message);
        
        // Log results
        console.log('📊 SMS Results:', {
            type: notificationType,
            totalRecipients: smsRecipients.length,
            sent: result.totalSent,
            failed: result.totalFailed
        });

        return result;
    }

    // Get SMS statistics
    async getStatistics() {
        // In production, fetch from SMS provider API
        return {
            totalSent: 0,
            totalFailed: 0,
            balance: 100.00, // TND
            lastMonth: {
                sent: 0,
                cost: 0.00
            }
        };
    }
}

// Create global SMS service instance
window.SMSService = new SMSService();

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SMSService;
}
