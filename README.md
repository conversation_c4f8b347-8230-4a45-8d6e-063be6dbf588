# 🍽️ ISA Meal Vote Platform

A comprehensive meal voting platform for ISA students to vote on daily lunch menus and confirm participation to reduce food waste.

## 🎯 Features

### For Students
- **Daily Voting**: Vote for tomorrow's lunch menu until 13:30
- **Participation Confirmation**: Confirm if you'll actually eat the meal
- **Real-time Results**: See voting progress and winning meals
- **Mobile Responsive**: Works on all devices

### For Administrators
- **Complete Dashboard**: Manage all aspects of the voting system
- **User Management**: Import/export users, manage authorized emails
- **Notification System**: Send targeted reminders to non-voters
- **Statistics & Analytics**: Detailed voting and participation statistics
- **Vote Generation**: Generate test votes for system testing
- **System Settings**: Configure voting thresholds and schedules

## 🏗️ Project Structure

```
├── public/
│   ├── index.html              # Main student voting app
│   ├── app.js                  # Main application logic
│   ├── admin-dashboard.html    # Complete admin dashboard
│   ├── admin-dashboard.js      # Admin dashboard logic
│   ├── admin-stats.html        # Detailed statistics page
│   ├── quick-admin.html        # Quick survey creation
│   ├── system-settings.html    # System configuration
│   ├── vote-generator.html     # Test vote generation tool
│   ├── firebase-config.js      # Firebase configuration
│   ├── images.js              # Menu images data
│   └── logo.svg               # ISA logo
├── firebase.json              # Firebase hosting config
├── firestore.rules           # Firestore security rules
└── package.json              # Node.js dependencies
```

## 🚀 Quick Start

### Prerequisites
- Node.js installed
- Firebase CLI installed (`npm install -g firebase-tools`)
- Firebase project created

### Setup
1. Clone the repository
2. Install dependencies: `npm install`
3. Configure Firebase: Update `firebase-config.js` with your project config
4. Deploy: `firebase deploy`

### Admin Access
- **URL**: `https://your-project.web.app/admin-dashboard.html`
- **Password**: Configure in `admin-dashboard.js` (ADMIN_PASSWORD)

## 📱 Usage

### Daily Workflow
1. **Admin creates survey** (quick-admin.html) - Usually done evening before
2. **Students vote** (index.html) - Until 13:30 daily
3. **System shows results** - After 13:30
4. **Students confirm participation** - Until midnight
5. **Kitchen gets final count** - Next morning

### Key URLs
- **Student App**: `https://your-project.web.app/`
- **Admin Dashboard**: `https://your-project.web.app/admin-dashboard.html`
- **Statistics**: `https://your-project.web.app/admin-stats.html`
- **Settings**: `https://your-project.web.app/system-settings.html`

## ⚙️ Configuration

### System Settings
- **Total Students**: Number of students in school
- **Vote Threshold**: Minimum votes needed to confirm meal
- **Voting Deadline**: Daily voting closes at 13:30
- **Participation Deadline**: Final confirmation at midnight

### Menu Configuration
Menus are configured in `images.js` with:
- Monday through Saturday menus
- Tunisian food options
- Food images and descriptions

## 🔧 Admin Features

### Dashboard Tabs
1. **📊 Overview**: Quick statistics and recent activity
2. **👥 User Management**: View and manage registered users
3. **📧 Email Management**: Import/export authorized emails
4. **⚙️ Settings**: System configuration and notifications

### Notification System
- **Voting Reminders**: Target users who haven't voted
- **Participation Reminders**: Target undecided users
- **Custom Messages**: Send personalized notifications
- **Results Announcements**: Announce winning meals

### Data Management
- **Import Users**: Automatically import from voting data
- **Export Data**: CSV export of emails and votes
- **Vote Generation**: Create test votes for system testing
- **Statistics**: Detailed analytics and reporting

## 🔒 Security

### Authentication
- **Student Access**: Firebase Authentication with email verification
- **Admin Access**: Password-protected admin dashboard
- **Email Restrictions**: Optional whitelist of authorized emails

### Firestore Rules
- Students can only read/write their own votes
- Admin collections have appropriate access controls
- Data validation and security rules implemented

## 📊 Analytics

### Tracked Metrics
- **Voting Participation**: Daily voting rates
- **Meal Preferences**: Popular menu items
- **Participation Accuracy**: Vote vs actual participation
- **Threshold Achievement**: Success rates for meal confirmation

### Reports Available
- **Daily Statistics**: Votes, participation, preferences
- **User Activity**: Individual voting patterns
- **Waste Reduction**: Participation accuracy metrics
- **System Usage**: Overall platform engagement

## 🌟 Key Benefits

- **Reduces Food Waste**: Accurate participation prediction
- **Student Engagement**: Democratic meal selection
- **Efficient Planning**: Kitchen knows exact portions needed
- **Data-Driven**: Analytics for continuous improvement
- **Mobile-First**: Accessible on all devices
- **Scalable**: Handles growing student populations

## 🛠️ Technical Stack

- **Frontend**: HTML5, CSS3 (Tailwind), JavaScript (React-like components)
- **Backend**: Firebase (Firestore, Authentication, Hosting)
- **Security**: Firestore Rules, Email verification
- **Analytics**: Built-in statistics and reporting
- **Mobile**: Responsive design, PWA-ready

## 📞 Support

For technical support or feature requests, contact the development team.

---

**Built with ❤️ for ISA students to reduce food waste and improve meal planning.**
