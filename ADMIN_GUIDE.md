# 👨‍💼 ISA Meal Vote - Admin Guide

Complete guide for administrators to manage the meal voting platform.

## 🔐 Admin Access

**Dashboard URL**: https://isa-meal-vote.web.app/admin-dashboard.html
**Password**: `Z62342` (configurable in admin-dashboard.js)

## 📋 Daily Workflow

### Morning Routine (8:00 AM)
1. **Check yesterday's results** in Statistics
2. **Create today's survey** if not already done
3. **Send voting reminders** if needed

### Afternoon (13:30)
1. **Check voting results** - voting closes automatically
2. **Announce winning meal** to all students
3. **Monitor participation confirmations**

### Evening (23:59)
1. **Final participation count** for kitchen
2. **Prepare tomorrow's survey** for next day

## 🎯 Main Dashboard Features

### 📊 Overview Tab
- **Quick Statistics**: Users, surveys, participants
- **Recent Activity**: Latest votes and surveys
- **Database Status**: Connection and sync info

### 👥 User Management Tab
- **View All Users**: See everyone who has voted
- **User Details**: Voting history and participation
- **Remove Users**: Delete users and their data
- **Refresh Data**: Update user list

### 📧 Email Management Tab
- **Import Users**: Auto-import from voting database
- **Add Emails**: Manually add authorized emails
- **Remove Emails**: Delete from authorized list
- **Export Data**: Download emails and votes as CSV
- **Bulk Actions**: Import CSV, clear all data

### ⚙️ Settings Tab
- **System Configuration**: Total students, thresholds
- **Notification System**: Send targeted messages
- **Recent Notifications**: View sent messages
- **Quick Actions**: Voting reminders, announcements

## 📢 Notification System

### Quick Actions
- **⏰ Remind Non-Voters**: Send to users who haven't voted
- **🍽️ Remind Undecided**: Send to users who haven't confirmed
- **🎉 Announce Results**: Send winning meal to everyone

### Custom Notifications
1. **Enter Title**: Short, clear subject
2. **Write Message**: Detailed notification content
3. **Select Target**: Choose recipient group
4. **Choose Method**: Email, SMS, or Push
5. **Preview**: Check message before sending
6. **Send**: Deliver to selected users

### Target Groups
- **📧 All Users**: Everyone in authorized list
- **❌ Haven't Voted**: Users who need to vote
- **✅ Voted Today**: Users who already voted
- **🍽️ Will Participate**: Confirmed participants
- **🚫 Won't Participate**: Users who declined
- **⏳ Undecided**: Users who haven't confirmed

## 🗳️ Survey Management

### Creating Surveys
1. **Go to Quick Admin**: https://isa-meal-vote.web.app/quick-admin.html
2. **Set Parameters**:
   - Total Students: Usually 100
   - Votes Required: Usually 25 (adjustable)
3. **Click "Create Tomorrow's Menu Survey"**
4. **Verify Creation**: Check in dashboard statistics

### Survey Phases
- **🗳️ Voting**: Until 13:30 daily
- **🏆 Results**: 13:30-14:00 (30 minutes)
- **✋ Participation**: 14:00-23:59
- **🔒 Closed**: After midnight

### Managing Phases
- **Auto-Update**: Click "🕐 Check Time & Update Phases"
- **Manual Control**: Phases update based on time
- **Monitor Progress**: Watch in statistics page

## 📊 Statistics & Analytics

### Key Metrics
- **Voting Rate**: Percentage of students who voted
- **Participation Rate**: Confirmed vs actual participants
- **Popular Meals**: Most voted menu items
- **Waste Reduction**: Accuracy of predictions

### Viewing Statistics
1. **Go to Statistics**: https://isa-meal-vote.web.app/admin-stats.html
2. **Select Survey**: Choose from dropdown
3. **View Details**: Votes, participation, breakdown
4. **Export Data**: Download for analysis

## ⚙️ System Configuration

### Threshold Settings
1. **Go to System Settings**: https://isa-meal-vote.web.app/system-settings.html
2. **Configure**:
   - Total Students: Actual school enrollment
   - Votes Required: Minimum for meal confirmation
   - Voting Time: Usually 13:30
   - Participation Time: Usually 23:59
3. **Use Presets**: Small/Medium/Large school templates
4. **Save Settings**: Apply configuration

### Recommended Thresholds
- **Small School (50 students)**: 15 votes (30%)
- **Medium School (100 students)**: 25 votes (25%)
- **Large School (200 students)**: 50 votes (25%)

## 🧪 Testing & Development

### Generate Test Votes
1. **Go to Vote Generator**: https://isa-meal-vote.web.app/vote-generator.html
2. **Select Survey**: Choose active survey
3. **Set Parameters**:
   - Number of Votes: 24+ for testing
   - Email Domain: student.isa.tn
   - Participation Distribution: 70/20/10
4. **Generate**: Create test votes
5. **Verify**: Check in dashboard statistics

### Test Scenarios
- **Low Participation**: Generate 15 votes, see threshold not met
- **High Participation**: Generate 30 votes, see threshold exceeded
- **Mixed Participation**: Test different confirmation rates

## 🔧 Troubleshooting

### Common Issues

**"No surveys found"**
- Create a survey using Quick Admin
- Check if survey was created successfully
- Verify Firebase connection

**"Error loading data"**
- Check Firebase connection with Test button
- Verify Firestore rules are deployed
- Refresh page and try again

**"Users not showing"**
- Import users from database
- Check if anyone has voted yet
- Verify email data in votes

**"Notifications not working"**
- Check recipient count before sending
- Verify target group has users
- Check notification history

### Firebase Console Access
- **URL**: https://console.firebase.google.com/project/isa-meal-vote
- **Check**: Firestore data, authentication, hosting
- **Monitor**: Usage, errors, performance

## 📱 Mobile Management

### Admin Dashboard Mobile
- **Responsive Design**: Works on tablets and phones
- **Touch Friendly**: Large buttons and inputs
- **Quick Actions**: Essential functions accessible
- **Offline Capable**: Basic functions work offline

### Best Practices
- **Use tablet** for better admin experience
- **Portrait mode** for forms and lists
- **Landscape mode** for statistics and charts
- **Bookmark** admin dashboard for quick access

## 🔒 Security Best Practices

### Password Management
- **Change Default**: Update ADMIN_PASSWORD in code
- **Use Strong Password**: Mix of letters, numbers, symbols
- **Regular Updates**: Change password periodically
- **Secure Storage**: Don't share or write down

### Data Protection
- **Regular Backups**: Export data periodically
- **Access Control**: Limit admin access
- **Monitor Usage**: Check for unusual activity
- **Update Rules**: Review Firestore security rules

## 📞 Support & Maintenance

### Regular Tasks
- **Weekly**: Review statistics and participation rates
- **Monthly**: Export data for analysis
- **Quarterly**: Review and update system settings
- **Annually**: Update menu options and images

### Getting Help
- **Check Logs**: Browser console (F12) for errors
- **Test Connection**: Use built-in Firebase test
- **Documentation**: Refer to this guide and README
- **Firebase Console**: Check backend status

---

**Remember**: The goal is to reduce food waste while giving students choice in their meals. Monitor participation accuracy and adjust thresholds as needed for optimal results.
