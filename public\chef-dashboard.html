<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chef Dashboard - ISA Kitchen</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-firestore-compat.js"></script>
    <script src="./firebase-config.js"></script>
</head>
<body class="bg-gradient-to-br from-orange-50 via-white to-red-50 min-h-screen">
    <div id="loginScreen" class="min-h-screen flex items-center justify-center">
        <div class="bg-white p-8 rounded-2xl shadow-xl max-w-md w-full mx-4 border-t-4 border-orange-600">
            <!-- Chef <PERSON> and Header -->
            <div class="text-center mb-6">
                <div class="w-20 h-20 mx-auto mb-3 rounded-xl shadow-lg bg-gradient-to-br from-orange-600 to-red-600 flex items-center justify-center border-2 border-yellow-400">
                    <div class="text-center">
                        <div class="text-white font-bold text-lg">ISA</div>
                        <div class="text-white text-xs">Kitchen</div>
                        <div class="text-2xl">👨‍🍳</div>
                    </div>
                </div>
                <h1 class="text-2xl font-bold text-gray-800">Chef Dashboard</h1>
                <p class="text-gray-600 text-sm">Kitchen Management System</p>
            </div>

            <div id="errorMessage" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm hidden"></div>

            <form onsubmit="chefLogin(event)" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Chef Password</label>
                    <input type="password" id="chefPassword" class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="Enter chef password" required>
                </div>
                <button type="submit" class="w-full bg-gradient-to-r from-orange-600 to-red-600 text-white py-3 rounded-lg font-semibold hover:from-orange-700 hover:to-red-700 transition-all shadow-lg">
                    🔓 Access Kitchen Dashboard
                </button>
            </form>
        </div>
    </div>

    <div id="dashboardScreen" class="hidden">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg border-b-4 border-orange-500">
            <div class="container mx-auto px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 rounded-xl shadow-md bg-gradient-to-br from-orange-600 to-red-600 flex items-center justify-center border-2 border-yellow-400">
                            <span class="text-white text-2xl">👨‍🍳</span>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-800">ISA Kitchen Dashboard</h1>
                            <p class="text-gray-600 text-sm">Chef Management System</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <div class="text-sm font-semibold text-gray-800" id="currentDate"></div>
                            <div class="text-xs text-gray-600" id="currentTime"></div>
                        </div>
                        <button onclick="chefLogout()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                            🔒 Logout
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container mx-auto px-6 py-8">
            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500">
                    <div class="flex items-center">
                        <div class="text-3xl mr-4">📊</div>
                        <div>
                            <div class="text-2xl font-bold text-blue-600" id="totalVotes">0</div>
                            <div class="text-sm text-gray-600">Total Votes</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
                    <div class="flex items-center">
                        <div class="text-3xl mr-4">🍽️</div>
                        <div>
                            <div class="text-2xl font-bold text-green-600" id="totalParticipants">0</div>
                            <div class="text-sm text-gray-600">Will Eat</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500">
                    <div class="flex items-center">
                        <div class="text-3xl mr-4">❌</div>
                        <div>
                            <div class="text-2xl font-bold text-red-600" id="totalDeclined">0</div>
                            <div class="text-sm text-gray-600">Won't Eat</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-yellow-500">
                    <div class="flex items-center">
                        <div class="text-3xl mr-4">⏳</div>
                        <div>
                            <div class="text-2xl font-bold text-yellow-600" id="totalPending">0</div>
                            <div class="text-sm text-gray-600">Pending</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Today's Menu -->
            <div class="bg-white rounded-xl shadow-xl p-8 mb-8 border-t-4 border-orange-500">
                <div class="text-center mb-6">
                    <h2 class="text-3xl font-bold text-gray-800 mb-2">🍽️ Today's Menu</h2>
                    <p class="text-gray-600">What to prepare for tomorrow</p>
                </div>

                <div id="menuContent">
                    <div class="text-center py-8">
                        <div class="text-6xl mb-4">⏳</div>
                        <p class="text-gray-500">Loading menu information...</p>
                    </div>
                </div>
            </div>

            <!-- Preparation Instructions -->
            <div class="bg-white rounded-xl shadow-xl p-8 mb-8 border-t-4 border-green-500">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">📋 Preparation Instructions</h3>
                <div id="preparationInstructions">
                    <div class="text-center py-4">
                        <p class="text-gray-500">Instructions will appear once menu is determined</p>
                    </div>
                </div>
            </div>

            <!-- Shopping List -->
            <div class="bg-white rounded-xl shadow-xl p-8 border-t-4 border-purple-500">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">🛒 Shopping List</h3>
                <div id="shoppingList">
                    <div class="text-center py-4">
                        <p class="text-gray-500">Shopping list will be generated based on portions needed</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Firebase
        const app = firebase.initializeApp(window.firebaseConfig);
        const db = firebase.firestore();

        // Chef password (in production, this should be more secure)
        const CHEF_PASSWORD = 'chef2024';

        let currentSurvey = null;
        let surveyStats = {
            totalVotes: 0,
            totalParticipants: 0,
            totalDeclined: 0,
            totalPending: 0
        };

        // Update time display
        function updateTime() {
            const now = new Date();
            document.getElementById('currentDate').textContent = now.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            document.getElementById('currentTime').textContent = now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        // Chef login
        function chefLogin(event) {
            event.preventDefault();
            const password = document.getElementById('chefPassword').value;
            const errorDiv = document.getElementById('errorMessage');

            if (password === CHEF_PASSWORD) {
                document.getElementById('loginScreen').classList.add('hidden');
                document.getElementById('dashboardScreen').classList.remove('hidden');
                loadKitchenData();
                setInterval(updateTime, 1000);
                updateTime();
            } else {
                errorDiv.textContent = '❌ Invalid password. Please try again.';
                errorDiv.classList.remove('hidden');
                document.getElementById('chefPassword').value = '';
            }
        }

        // Chef logout
        function chefLogout() {
            document.getElementById('loginScreen').classList.remove('hidden');
            document.getElementById('dashboardScreen').classList.add('hidden');
            document.getElementById('chefPassword').value = '';
            document.getElementById('errorMessage').classList.add('hidden');
        }

        // Load kitchen data
        async function loadKitchenData() {
            try {
                console.log('Loading kitchen data...');
                
                // Get current survey
                const surveysSnap = await db.collection('surveys').orderBy('createdAt', 'desc').limit(1).get();
                
                if (surveysSnap.empty) {
                    showNoMenuMessage();
                    return;
                }

                currentSurvey = { id: surveysSnap.docs[0].id, ...surveysSnap.docs[0].data() };
                
                // Get votes and participation data
                const votesSnap = await db.collection('surveys').doc(currentSurvey.id).collection('votes').get();
                
                // Calculate statistics
                let totalVotes = 0;
                let totalParticipants = 0;
                let totalDeclined = 0;
                let totalPending = 0;

                votesSnap.docs.forEach(doc => {
                    const vote = doc.data();
                    if (vote.optionId) totalVotes++; // Only count actual votes, not late joiners
                    
                    if (vote.participate === true) {
                        totalParticipants++;
                    } else if (vote.participate === false) {
                        totalDeclined++;
                    } else {
                        totalPending++;
                    }
                });

                surveyStats = {
                    totalVotes,
                    totalParticipants,
                    totalDeclined,
                    totalPending
                };

                updateStatsDisplay();
                displayMenu();
                generatePreparationInstructions();
                generateShoppingList();

            } catch (error) {
                console.error('Error loading kitchen data:', error);
                showErrorMessage('Error loading kitchen data: ' + error.message);
            }
        }

        // Update stats display
        function updateStatsDisplay() {
            document.getElementById('totalVotes').textContent = surveyStats.totalVotes;
            document.getElementById('totalParticipants').textContent = surveyStats.totalParticipants;
            document.getElementById('totalDeclined').textContent = surveyStats.totalDeclined;
            document.getElementById('totalPending').textContent = surveyStats.totalPending;
        }

        // Display menu
        function displayMenu() {
            const menuDiv = document.getElementById('menuContent');
            
            if (!currentSurvey.winningOption) {
                menuDiv.innerHTML = `
                    <div class="text-center py-8">
                        <div class="text-6xl mb-4">⏳</div>
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">Voting Still Open</h3>
                        <p class="text-gray-500">Menu will be determined when voting closes at 13:30</p>
                    </div>
                `;
                return;
            }

            const emoji = getFoodEmoji(currentSurvey.winningOption.text);
            const statusColor = currentSurvey.thresholdMet ? 'green' : 'orange';
            const statusText = currentSurvey.thresholdMet ? 'Confirmed' : 'Low Participation';
            const statusIcon = currentSurvey.thresholdMet ? '✅' : '⚠️';

            menuDiv.innerHTML = `
                <div class="bg-gradient-to-r from-${statusColor}-50 to-${statusColor}-100 rounded-xl p-8 border-2 border-${statusColor}-200">
                    <div class="text-center mb-6">
                        <div class="text-8xl mb-4">${emoji}</div>
                        <h3 class="text-3xl font-bold text-gray-800 mb-2">${currentSurvey.winningOption.text}</h3>
                        <div class="flex items-center justify-center space-x-2 mb-4">
                            <span class="text-2xl">${statusIcon}</span>
                            <span class="text-lg font-semibold text-${statusColor}-700">${statusText}</span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                        <div class="bg-white rounded-lg p-4 shadow-inner">
                            <div class="text-2xl font-bold text-blue-600">${surveyStats.totalVotes}</div>
                            <div class="text-sm text-gray-600">Total Votes</div>
                        </div>
                        <div class="bg-white rounded-lg p-4 shadow-inner">
                            <div class="text-3xl font-bold text-green-600">${surveyStats.totalParticipants}</div>
                            <div class="text-sm text-gray-600">Portions to Prepare</div>
                        </div>
                        <div class="bg-white rounded-lg p-4 shadow-inner">
                            <div class="text-2xl font-bold text-red-600">${surveyStats.totalDeclined}</div>
                            <div class="text-sm text-gray-600">Won't Eat</div>
                        </div>
                    </div>
                    
                    ${surveyStats.totalPending > 0 ? `
                        <div class="mt-4 p-3 bg-yellow-100 rounded-lg text-center">
                            <span class="text-yellow-800 font-semibold">⏳ ${surveyStats.totalPending} students haven't confirmed yet</span>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // Get food emoji
        function getFoodEmoji(text) {
            const lowerText = text.toLowerCase();
            if (lowerText.includes('couscous')) return '🍲';
            if (lowerText.includes('brik')) return '🥟';
            if (lowerText.includes('tajine')) return '🍛';
            if (lowerText.includes('fish') || lowerText.includes('salmon') || lowerText.includes('sea bass')) return '🐟';
            if (lowerText.includes('meat') || lowerText.includes('lamb') || lowerText.includes('beef')) return '🥩';
            if (lowerText.includes('chicken')) return '🍗';
            if (lowerText.includes('soup') || lowerText.includes('chorba')) return '🍜';
            if (lowerText.includes('salad') || lowerText.includes('mechouia')) return '🥗';
            if (lowerText.includes('sandwich') || lowerText.includes('casse-croûte')) return '🥪';
            if (lowerText.includes('pasta') || lowerText.includes('rechta')) return '🍝';
            if (lowerText.includes('pizza')) return '🍕';
            return '🍽️';
        }

        // Generate preparation instructions
        function generatePreparationInstructions() {
            const instructionsDiv = document.getElementById('preparationInstructions');
            
            if (!currentSurvey.winningOption) {
                instructionsDiv.innerHTML = '<p class="text-gray-500">Instructions will appear once menu is determined</p>';
                return;
            }

            const portions = surveyStats.totalParticipants;
            const mealName = currentSurvey.winningOption.text;
            
            instructionsDiv.innerHTML = `
                <div class="space-y-4">
                    <div class="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500">
                        <h4 class="font-semibold text-blue-800 mb-2">📊 Preparation Summary</h4>
                        <ul class="text-blue-700 space-y-1">
                            <li>• <strong>Meal:</strong> ${mealName}</li>
                            <li>• <strong>Portions needed:</strong> ${portions} servings</li>
                            <li>• <strong>Extra portions:</strong> ${Math.ceil(portions * 0.1)} (10% buffer)</li>
                            <li>• <strong>Total to prepare:</strong> ${portions + Math.ceil(portions * 0.1)} servings</li>
                        </ul>
                    </div>
                    
                    <div class="bg-green-50 rounded-lg p-4 border-l-4 border-green-500">
                        <h4 class="font-semibold text-green-800 mb-2">⏰ Timing</h4>
                        <ul class="text-green-700 space-y-1">
                            <li>• Start preparation: 8:00 AM</li>
                            <li>• Cooking time: 2-3 hours</li>
                            <li>• Ready to serve: 12:00 PM</li>
                            <li>• Service time: 12:00 PM - 2:00 PM</li>
                        </ul>
                    </div>
                    
                    <div class="bg-orange-50 rounded-lg p-4 border-l-4 border-orange-500">
                        <h4 class="font-semibold text-orange-800 mb-2">👨‍🍳 Chef Notes</h4>
                        <ul class="text-orange-700 space-y-1">
                            <li>• Prepare ${portions + Math.ceil(portions * 0.1)} portions total</li>
                            <li>• Check ingredient availability before starting</li>
                            <li>• Keep extra portions warm for late arrivals</li>
                            <li>• Monitor participation confirmations until midnight</li>
                        </ul>
                    </div>
                </div>
            `;
        }

        // Generate shopping list
        function generateShoppingList() {
            const shoppingDiv = document.getElementById('shoppingList');
            
            if (!currentSurvey.winningOption) {
                shoppingDiv.innerHTML = '<p class="text-gray-500">Shopping list will be generated based on portions needed</p>';
                return;
            }

            const portions = surveyStats.totalParticipants + Math.ceil(surveyStats.totalParticipants * 0.1);
            const mealName = currentSurvey.winningOption.text.toLowerCase();
            
            let ingredients = [];
            
            // Generate ingredients based on meal type
            if (mealName.includes('couscous')) {
                ingredients = [
                    { item: 'Couscous grain', quantity: `${Math.ceil(portions * 0.15)} kg`, category: 'Grains' },
                    { item: 'Lamb meat', quantity: `${Math.ceil(portions * 0.2)} kg`, category: 'Meat' },
                    { item: 'Vegetables (carrots, zucchini, turnips)', quantity: `${Math.ceil(portions * 0.3)} kg`, category: 'Vegetables' },
                    { item: 'Chickpeas', quantity: `${Math.ceil(portions * 0.1)} kg`, category: 'Legumes' },
                    { item: 'Onions', quantity: `${Math.ceil(portions * 0.05)} kg`, category: 'Vegetables' },
                    { item: 'Spices (ras el hanout, salt, pepper)', quantity: 'As needed', category: 'Spices' }
                ];
            } else if (mealName.includes('brik')) {
                ingredients = [
                    { item: 'Brik pastry sheets', quantity: `${portions} pieces`, category: 'Pastry' },
                    { item: 'Eggs', quantity: `${portions} pieces`, category: 'Dairy' },
                    { item: 'Tuna', quantity: `${Math.ceil(portions * 0.1)} kg`, category: 'Fish' },
                    { item: 'Potatoes', quantity: `${Math.ceil(portions * 0.2)} kg`, category: 'Vegetables' },
                    { item: 'Parsley', quantity: '200g', category: 'Herbs' },
                    { item: 'Oil for frying', quantity: '2L', category: 'Oil' }
                ];
            } else {
                // Generic ingredients
                ingredients = [
                    { item: 'Main ingredient', quantity: `${Math.ceil(portions * 0.25)} kg`, category: 'Main' },
                    { item: 'Vegetables', quantity: `${Math.ceil(portions * 0.2)} kg`, category: 'Vegetables' },
                    { item: 'Rice/Pasta/Bread', quantity: `${Math.ceil(portions * 0.15)} kg`, category: 'Carbs' },
                    { item: 'Spices and seasonings', quantity: 'As needed', category: 'Spices' }
                ];
            }
            
            const categories = [...new Set(ingredients.map(ing => ing.category))];
            
            let shoppingHTML = '<div class="space-y-6">';
            
            categories.forEach(category => {
                const categoryItems = ingredients.filter(ing => ing.category === category);
                shoppingHTML += `
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-3">${category}</h4>
                        <ul class="space-y-2">
                `;
                
                categoryItems.forEach(item => {
                    shoppingHTML += `
                        <li class="flex justify-between items-center">
                            <span class="text-gray-700">• ${item.item}</span>
                            <span class="font-semibold text-blue-600">${item.quantity}</span>
                        </li>
                    `;
                });
                
                shoppingHTML += '</ul></div>';
            });
            
            shoppingHTML += `
                <div class="bg-yellow-50 rounded-lg p-4 border-l-4 border-yellow-500">
                    <h4 class="font-semibold text-yellow-800 mb-2">📝 Shopping Notes</h4>
                    <ul class="text-yellow-700 space-y-1 text-sm">
                        <li>• Quantities calculated for ${portions} portions (including 10% buffer)</li>
                        <li>• Check current stock before purchasing</li>
                        <li>• Buy fresh ingredients on the day of preparation</li>
                        <li>• Keep receipts for cost tracking</li>
                    </ul>
                </div>
            </div>`;
            
            shoppingDiv.innerHTML = shoppingHTML;
        }

        // Show no menu message
        function showNoMenuMessage() {
            document.getElementById('menuContent').innerHTML = `
                <div class="text-center py-8">
                    <div class="text-6xl mb-4">🍽️</div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">No Menu Available</h3>
                    <p class="text-gray-500">No surveys have been created yet. Check back later.</p>
                </div>
            `;
        }

        // Show error message
        function showErrorMessage(message) {
            document.getElementById('menuContent').innerHTML = `
                <div class="text-center py-8">
                    <div class="text-6xl mb-4">❌</div>
                    <h3 class="text-xl font-semibold text-red-700 mb-2">Error</h3>
                    <p class="text-red-500">${message}</p>
                </div>
            `;
        }

        // Auto-refresh data every 30 seconds
        setInterval(loadKitchenData, 30000);
    </script>
</body>
</html>
