<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Settings - ISA Meal Vote</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.0/firebase-firestore-compat.js"></script>
    <script src="./firebase-config.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-6 py-8">
        <div class="bg-white rounded-xl shadow-xl p-8 max-w-4xl mx-auto">
            <div class="text-center mb-8">
                <div class="w-16 h-16 mx-auto mb-4 rounded-xl shadow-md bg-gradient-to-br from-indigo-600 to-indigo-700 flex items-center justify-center">
                    <span class="text-white text-2xl">⚙️</span>
                </div>
                <h1 class="text-3xl font-bold text-gray-800">System Settings</h1>
                <p class="text-gray-600 mt-2">Configure voting thresholds and system parameters</p>
            </div>

            <!-- Current Settings Display -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-blue-800 mb-4">📊 Current Settings</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600" id="currentTotalStudents">100</div>
                        <div class="text-sm text-blue-700">Total Students</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600" id="currentThreshold">25</div>
                        <div class="text-sm text-green-700">Votes Required</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600" id="currentPercentage">25%</div>
                        <div class="text-sm text-purple-700">Threshold Percentage</div>
                    </div>
                </div>
            </div>

            <!-- Settings Form -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Basic Settings -->
                <div class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-800">Basic Configuration</h3>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Total Students in School</label>
                        <input type="number" id="totalStudents" value="100" min="1" max="2000" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                               onchange="updateCalculations()">
                        <p class="text-xs text-gray-500 mt-1">Total number of students who can vote</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Votes Required (Threshold)</label>
                        <input type="number" id="voteThreshold" value="25" min="1" max="2000" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                               onchange="updateCalculations()">
                        <p class="text-xs text-gray-500 mt-1">Minimum votes needed to confirm meal preparation</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Voting Deadline Time</label>
                        <input type="time" id="votingTime" value="13:30" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <p class="text-xs text-gray-500 mt-1">Daily voting closes at this time</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Participation Deadline Time</label>
                        <input type="time" id="participationTime" value="23:59" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        <p class="text-xs text-gray-500 mt-1">Final participation confirmation deadline</p>
                    </div>
                </div>

                <!-- Quick Presets -->
                <div class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-800">Quick Presets</h3>
                    
                    <div class="space-y-3">
                        <button onclick="applyPreset('small')" class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 text-left px-4">
                            <div class="font-semibold">🏫 Small School</div>
                            <div class="text-sm opacity-90">50 students • 15 votes needed (30%)</div>
                        </button>
                        
                        <button onclick="applyPreset('medium')" class="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 text-left px-4">
                            <div class="font-semibold">🏢 Medium School</div>
                            <div class="text-sm opacity-90">100 students • 25 votes needed (25%)</div>
                        </button>
                        
                        <button onclick="applyPreset('large')" class="w-full bg-purple-600 text-white py-3 rounded-lg hover:bg-purple-700 text-left px-4">
                            <div class="font-semibold">🏛️ Large School</div>
                            <div class="text-sm opacity-90">200 students • 50 votes needed (25%)</div>
                        </button>
                        
                        <button onclick="applyPreset('percentage')" class="w-full bg-orange-600 text-white py-3 rounded-lg hover:bg-orange-700 text-left px-4">
                            <div class="font-semibold">📊 50% Threshold</div>
                            <div class="text-sm opacity-90">Auto-calculate 50% of total students</div>
                        </button>
                    </div>

                    <!-- Calculation Helper -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-3">💡 Calculation Helper</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>25% threshold:</span>
                                <span id="calc25" class="font-semibold">25 votes</span>
                            </div>
                            <div class="flex justify-between">
                                <span>30% threshold:</span>
                                <span id="calc30" class="font-semibold">30 votes</span>
                            </div>
                            <div class="flex justify-between">
                                <span>50% threshold:</span>
                                <span id="calc50" class="font-semibold">50 votes</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="mt-8 space-y-4">
                <button onclick="saveSettings()" class="w-full bg-indigo-600 text-white py-4 rounded-lg hover:bg-indigo-700 font-semibold text-lg">
                    💾 Save Settings
                </button>
                
                <div class="grid grid-cols-2 gap-4">
                    <button onclick="loadCurrentSettings()" class="bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700">
                        🔄 Reload Current
                    </button>
                    <button onclick="resetToDefaults()" class="bg-red-600 text-white py-2 rounded-lg hover:bg-red-700">
                        🔄 Reset to Defaults
                    </button>
                </div>
            </div>

            <!-- Status Messages -->
            <div id="statusMessages" class="mt-6 space-y-2"></div>

            <!-- Navigation -->
            <div class="mt-8 text-center space-x-4">
                <a href="./quick-admin.html" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700">
                    📊 Create Survey
                </a>
                <a href="./admin-dashboard.html" class="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700">
                    👨‍💼 Admin Dashboard
                </a>
                <a href="./vote-generator.html" class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700">
                    🗳️ Generate Votes
                </a>
            </div>
        </div>
    </div>

    <script>
        // Initialize Firebase
        const app = firebase.initializeApp(window.firebaseConfig);
        const db = firebase.firestore();

        // Load settings on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentSettings();
            updateCalculations();
        });

        function updateCalculations() {
            const totalStudents = parseInt(document.getElementById('totalStudents').value) || 100;
            const threshold = parseInt(document.getElementById('voteThreshold').value) || 25;
            const percentage = Math.round((threshold / totalStudents) * 100);

            // Update current display
            document.getElementById('currentTotalStudents').textContent = totalStudents;
            document.getElementById('currentThreshold').textContent = threshold;
            document.getElementById('currentPercentage').textContent = percentage + '%';

            // Update calculation helper
            document.getElementById('calc25').textContent = Math.ceil(totalStudents * 0.25) + ' votes';
            document.getElementById('calc30').textContent = Math.ceil(totalStudents * 0.30) + ' votes';
            document.getElementById('calc50').textContent = Math.ceil(totalStudents * 0.50) + ' votes';
        }

        function applyPreset(preset) {
            switch(preset) {
                case 'small':
                    document.getElementById('totalStudents').value = 50;
                    document.getElementById('voteThreshold').value = 15;
                    break;
                case 'medium':
                    document.getElementById('totalStudents').value = 100;
                    document.getElementById('voteThreshold').value = 25;
                    break;
                case 'large':
                    document.getElementById('totalStudents').value = 200;
                    document.getElementById('voteThreshold').value = 50;
                    break;
                case 'percentage':
                    const total = parseInt(document.getElementById('totalStudents').value) || 100;
                    document.getElementById('voteThreshold').value = Math.ceil(total * 0.5);
                    break;
            }
            updateCalculations();
            addStatusMessage(`✅ Applied ${preset} preset`, 'success');
        }

        async function saveSettings() {
            try {
                const settings = {
                    totalStudents: parseInt(document.getElementById('totalStudents').value),
                    voteThreshold: parseInt(document.getElementById('voteThreshold').value),
                    votingTime: document.getElementById('votingTime').value,
                    participationTime: document.getElementById('participationTime').value,
                    updatedAt: firebase.firestore.FieldValue.serverTimestamp()
                };

                await db.collection('settings').doc('system').set(settings, { merge: true });
                addStatusMessage('🎉 Settings saved successfully!', 'success');

            } catch (error) {
                console.error('Error saving settings:', error);
                addStatusMessage('❌ Error saving settings: ' + error.message, 'error');
            }
        }

        async function loadCurrentSettings() {
            try {
                const doc = await db.collection('settings').doc('system').get();
                if (doc.exists) {
                    const settings = doc.data();
                    document.getElementById('totalStudents').value = settings.totalStudents || 100;
                    document.getElementById('voteThreshold').value = settings.voteThreshold || 25;
                    document.getElementById('votingTime').value = settings.votingTime || '13:30';
                    document.getElementById('participationTime').value = settings.participationTime || '23:59';
                    
                    updateCalculations();
                    addStatusMessage('✅ Settings loaded from database', 'success');
                } else {
                    addStatusMessage('ℹ️ No saved settings found, using defaults', 'info');
                }
            } catch (error) {
                console.error('Error loading settings:', error);
                addStatusMessage('❌ Error loading settings: ' + error.message, 'error');
            }
        }

        function resetToDefaults() {
            if (confirm('Reset all settings to defaults?')) {
                document.getElementById('totalStudents').value = 100;
                document.getElementById('voteThreshold').value = 25;
                document.getElementById('votingTime').value = '13:30';
                document.getElementById('participationTime').value = '23:59';
                
                updateCalculations();
                addStatusMessage('🔄 Settings reset to defaults', 'info');
            }
        }

        function addStatusMessage(message, type = 'info') {
            const container = document.getElementById('statusMessages');
            const div = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 
                           type === 'error' ? 'bg-red-50 border-red-200 text-red-800' : 
                           'bg-blue-50 border-blue-200 text-blue-800';
            
            div.className = `p-3 rounded-lg border ${bgColor}`;
            div.textContent = message;
            container.appendChild(div);
            
            // Remove after 5 seconds
            setTimeout(() => {
                if (div.parentNode) {
                    div.parentNode.removeChild(div);
                }
            }, 5000);
        }
    </script>
</body>
</html>
