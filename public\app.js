(function(){
  const { useState, useEffect } = React;

  // Initialize Firebase
  const app = firebase.initializeApp(window.firebaseConfig);
  const auth = firebase.auth();
  const db = firebase.firestore();

  function Login({ onLoggedIn }){
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [phoneNumber, setPhoneNumber] = useState("");
    const [enableNotifications, setEnableNotifications] = useState(true);
    const [isRegister, setIsRegister] = useState(false);
    const [error, setError] = useState("");
    const [info, setInfo] = useState("");

    const isValidTunisianPhone = (phone) => {
      // Tunisian mobile numbers: 8 digits starting with 2, 4, 5, or 9
      const tunisianMobileRegex = /^[2459]\d{7}$/;
      return tunisianMobileRegex.test(phone);
    };

    const submit = async (e) => {
      e.preventDefault();
      setError(""); setInfo("");
      try {
        if (isRegister) {
          // Validate phone number if provided
          if (phoneNumber && !isValidTunisianPhone(phoneNumber)) {
            setError('Please enter a valid Tunisian phone number (8 digits starting with 2, 4, 5, or 9)');
            return;
          }

          const cred = await auth.createUserWithEmailAndPassword(email, password);

          // Save user profile with phone and notification preferences
          if (phoneNumber || !enableNotifications) {
            await db.collection('userProfiles').doc(cred.user.uid).set({
              email: email,
              phoneNumber: phoneNumber ? `+216${phoneNumber}` : null,
              notifications: {
                enabled: enableNotifications,
                sms: phoneNumber ? enableNotifications : false,
                email: enableNotifications,
                voteReminder: enableNotifications,
                resultAnnouncement: enableNotifications,
                participationReminder: enableNotifications
              },
              createdAt: firebase.firestore.FieldValue.serverTimestamp(),
              country: 'Tunisia'
            });
          }

          await cred.user.sendEmailVerification();
          setInfo("Account created. Please verify your email, then sign in.");
        } else {
          await auth.signInWithEmailAndPassword(email, password);
          onLoggedIn();
        }
      } catch (err) { setError(err.message); }
    };

    return (
      React.createElement('div', { className: 'min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-green-50' },
        React.createElement('div', { className: 'bg-white p-8 rounded-2xl shadow-xl max-w-md w-full mx-4 border-t-4 border-blue-600' },
          // University Logo and Header
          React.createElement('div', { className: 'text-center mb-6' },
            React.createElement('div', { className: 'w-20 h-20 mx-auto mb-3 rounded-xl shadow-lg bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center border-2 border-red-500' },
              React.createElement('div', { className: 'text-center' },
                React.createElement('div', { className: 'text-white font-bold text-lg' }, 'ISA'),
                React.createElement('div', { className: 'text-white text-xs' }, 'Restaurant'),
                React.createElement('div', { className: 'text-lg' }, '🍽️')
              )
            ),
            React.createElement('h1', { className: 'text-2xl font-bold text-gray-900' }, 'ISA Meal Vote'),
            React.createElement('p', { className: 'text-gray-600 text-sm' }, 'University Restaurant System')
          ),
          info && React.createElement('div', { className: 'mb-3 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm' }, info),
          error && React.createElement('div', { className: 'mb-3 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm' }, error),
          React.createElement('form', { onSubmit: submit, className: 'space-y-4' },
            React.createElement('input', {
              className: 'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
              type: 'email', placeholder: 'University Email', value: email,
              onChange: (e)=>setEmail(e.target.value), required: true
            }),
            React.createElement('input', {
              className: 'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
              type: 'password', placeholder: 'Password', value: password,
              onChange: (e)=>setPassword(e.target.value), required: true
            }),

            // Phone number field (only for registration)
            isRegister && React.createElement('div', { className: 'space-y-2' },
              React.createElement('div', { className: 'flex' },
                React.createElement('div', { className: 'flex items-center px-3 py-3 bg-gray-50 border border-r-0 rounded-l-lg text-sm text-gray-600' },
                  '🇹🇳 +216'
                ),
                React.createElement('input', {
                  className: 'flex-1 px-4 py-3 border rounded-r-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                  type: 'tel', placeholder: '********', value: phoneNumber,
                  onChange: (e)=>setPhoneNumber(e.target.value.replace(/\D/g, '').slice(0, 8)),
                  maxLength: 8
                })
              ),
              React.createElement('p', { className: 'text-xs text-gray-500' },
                '📱 Optional: For SMS notifications (8 digits, starts with 2, 4, 5, or 9)'
              )
            ),

            // Notification preferences (only for registration)
            isRegister && React.createElement('div', { className: 'space-y-2' },
              React.createElement('label', { className: 'flex items-center space-x-3 p-3 bg-blue-50 rounded-lg border border-blue-200' },
                React.createElement('input', {
                  type: 'checkbox',
                  checked: enableNotifications,
                  onChange: (e)=>setEnableNotifications(e.target.checked),
                  className: 'w-4 h-4 text-blue-600 rounded focus:ring-blue-500'
                }),
                React.createElement('div', { className: 'flex-1' },
                  React.createElement('div', { className: 'text-sm font-medium text-blue-800' }, '🔔 Enable Notifications'),
                  React.createElement('div', { className: 'text-xs text-blue-600' },
                    'Get reminders to vote, meal results, and participation confirmations'
                  )
                )
              )
            ),

            React.createElement('button', {
              className: 'w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all shadow-lg'
            }, isRegister? 'Create Account' : 'Sign In'),
          ),
          React.createElement('div', { className: 'text-center mt-4' },
            React.createElement('button', {
              className: 'text-blue-600 hover:text-blue-800 text-sm font-medium',
              onClick: ()=>setIsRegister(!isRegister)
            }, isRegister? 'Already have an account? Sign In' : 'Need an account? Register'
            )
          )
        )
      )
    );
  }

  function SurveyApp({ user }){
    const [surveys, setSurveys] = useState([]);
    const [userVotes, setUserVotes] = useState({});
    const [loading, setLoading] = useState(true);
    const [showSettings, setShowSettings] = useState(false);

    const load = async () => {
      setLoading(true);
      try {
        console.log('Loading surveys...');
        const surveysSnap = await db.collection('surveys').get();
        console.log('Surveys snapshot:', surveysSnap.size, 'documents');
        let surveys = surveysSnap.docs.map(d=>({ id: d.id, ...d.data() }));

        // Sort by creation date (newest first) - should only be one active survey
        surveys.sort((a, b) => {
          const dateA = a.createdAt ? a.createdAt.toDate() : new Date(0);
          const dateB = b.createdAt ? b.createdAt.toDate() : new Date(0);
          return dateB - dateA;
        });

        // Load vote counts and check thresholds for each survey
        for (const survey of surveys) {
          const votesSnap = await db.collection('surveys').doc(survey.id).collection('votes').get();
          const totalVotes = votesSnap.size;
          const participantCount = votesSnap.docs.filter(doc => doc.data().participate).length;

          // Check current time and update survey phase
          const now = new Date();
          const currentHour = now.getHours();
          const currentMinute = now.getMinutes();
          const currentTime = currentHour * 60 + currentMinute; // Convert to minutes
          const votingDeadline = 13 * 60 + 30; // 13:30 in minutes
          const participationDeadline = 23 * 60 + 59; // 23:59 in minutes

          let newPhase = survey.phase;
          let shouldUpdate = false;

          // Auto-update phases based on time
          if (currentTime >= participationDeadline) {
            newPhase = 'closed';
            shouldUpdate = survey.phase !== 'closed';
          } else if (currentTime >= votingDeadline) {
            // After 13:30, move to participation phase regardless of vote count
            if (survey.phase === 'voting') {
              newPhase = 'participation';
              shouldUpdate = true;

              // Determine winning option even if threshold not met
              if (!survey.winningOption && survey.options && survey.options.length > 0) {
                const optionVotes = {};
                votesSnap.docs.forEach(doc => {
                  const vote = doc.data();
                  if (vote.optionId) {
                    optionVotes[vote.optionId] = (optionVotes[vote.optionId] || 0) + 1;
                  }
                });

                // Find option with most votes (or first option if tie/no votes)
                let winningOptionId = survey.options[0].id;
                let maxVotes = 0;

                for (const [optionId, votes] of Object.entries(optionVotes)) {
                  if (votes > maxVotes) {
                    maxVotes = votes;
                    winningOptionId = optionId;
                  }
                }

                const winningOption = survey.options.find(opt => opt.id === winningOptionId);

                // Update survey with winning option
                await db.collection('surveys').doc(survey.id).update({
                  phase: 'participation',
                  winningOption: winningOption,
                  votingClosedAt: firebase.firestore.FieldValue.serverTimestamp(),
                  finalVoteCount: totalVotes,
                  thresholdMet: totalVotes >= (survey.votesRequired || 25)
                });

                survey.winningOption = winningOption;
                survey.thresholdMet = totalVotes >= (survey.votesRequired || 25);
              }
            }
          } else {
            // During voting hours, check if threshold is met
            if (totalVotes >= (survey.votesRequired || 25) && survey.phase === 'voting') {
              newPhase = 'results';
              shouldUpdate = true;
            }
          }

          // Update phase if needed
          if (shouldUpdate && newPhase !== survey.phase) {
            await db.collection('surveys').doc(survey.id).update({
              phase: newPhase,
              updatedAt: firebase.firestore.FieldValue.serverTimestamp()
            });
            survey.phase = newPhase;
          }

          survey.totalVotes = totalVotes;
          survey.participantCount = participantCount;
          survey.thresholdMet = survey.threshold ? totalVotes >= survey.threshold : false;
          survey.progressPercent = survey.threshold ? Math.min((totalVotes / survey.threshold) * 100, 100) : 0;
        }

        console.log('Parsed surveys with vote counts:', surveys);
        setSurveys(surveys);

        // load own votes
        const votes = {};
        for (const s of surveys) {
          const vRef = db.collection('surveys').doc(s.id).collection('votes').doc(user.uid);
          const v = await vRef.get();
          if (v.exists) votes[s.id] = v.data();
        }
        setUserVotes(votes);
        console.log('User votes:', votes);
      } catch (error) {
        console.error('Error loading surveys:', error);
      }
      setLoading(false);
    };

    useEffect(()=>{ load(); }, []);

    // Initial vote (creates the vote document). Participation is initially unconfirmed (null).
    const vote = async (surveyId, optionId) => {
      const vRef = db.collection('surveys').doc(surveyId).collection('votes').doc(user.uid);
      await vRef.set({
        optionId,
        participate: null,
        createdAt: firebase.firestore.FieldValue.serverTimestamp(),
        userEmail: user.email, // Store email for admin dashboard
        userId: user.uid
      }, { merge: false });
      setUserVotes(prev=>({ ...prev, [surveyId]: { optionId, participate: null } }));

      // Show success message after voting
      setTimeout(() => {
        // Reload to show updated vote counts
        load();
      }, 1000);
    };

    // Update only participation field on existing vote
    const setParticipation = async (surveyId, participate) => {
      const vRef = db.collection('surveys').doc(surveyId).collection('votes').doc(user.uid);
      await vRef.update({
        participate: !!participate,
        updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
        userEmail: user.email // Ensure email is always stored
      });
      setUserVotes(prev=>({ ...prev, [surveyId]: { ...prev[surveyId], participate: !!participate } }));
    };

    // Allow non-voters to join the winning meal
    const joinMeal = async (surveyId, participate) => {
      const vRef = db.collection('surveys').doc(surveyId).collection('votes').doc(user.uid);

      // Create a participation-only entry for non-voters
      await vRef.set({
        optionId: null, // No vote cast
        participate: !!participate,
        createdAt: firebase.firestore.FieldValue.serverTimestamp(),
        userEmail: user.email,
        userId: user.uid,
        joinedLate: true // Flag to indicate this user joined after voting closed
      }, { merge: true });

      setUserVotes(prev=>({ ...prev, [surveyId]: { optionId: null, participate: !!participate, joinedLate: true } }));
    };

    // Get food emoji for menu item
    const getFoodEmoji = (text) => {
      const lowerText = text.toLowerCase();
      if (lowerText.includes('couscous')) return '🍲';
      if (lowerText.includes('brik')) return '🥟';
      if (lowerText.includes('tajine')) return '🍛';
      if (lowerText.includes('fish') || lowerText.includes('salmon') || lowerText.includes('sea bass')) return '🐟';
      if (lowerText.includes('meat') || lowerText.includes('lamb') || lowerText.includes('beef')) return '🥩';
      if (lowerText.includes('chicken')) return '🍗';
      if (lowerText.includes('soup') || lowerText.includes('chorba')) return '🍜';
      if (lowerText.includes('salad') || lowerText.includes('mechouia')) return '🥗';
      if (lowerText.includes('sandwich') || lowerText.includes('casse-croûte')) return '🥪';
      if (lowerText.includes('pasta') || lowerText.includes('rechta')) return '🍝';
      if (lowerText.includes('pizza')) return '🍕';
      return '🍽️'; // Default food emoji
    };

    if (loading) return React.createElement('div', { className: 'p-8 text-center' }, 'Loading...');

    return (
      React.createElement('div', { className: 'max-w-4xl mx-auto p-4' },
        // Header with University Logo and Chef
        React.createElement('div', { className: 'bg-white rounded-xl shadow-lg p-6 mb-6 border-t-4 border-blue-600' },
          React.createElement('div', { className: 'flex items-center justify-between' },
            React.createElement('div', { className: 'flex items-center space-x-4' },
              React.createElement('div', { className: 'w-14 h-14 rounded-xl shadow-md bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center border-2 border-red-500' },
                React.createElement('div', { className: 'text-center' },
                  React.createElement('div', { className: 'text-white font-bold text-sm' }, 'ISA'),
                  React.createElement('div', { className: 'text-xs' }, '🍽️')
                )
              ),
              React.createElement('div', null,
                React.createElement('h1', { className: 'text-2xl font-bold text-gray-800' }, 'ISA Restaurant'),
                React.createElement('p', { className: 'text-gray-600' }, `Welcome, ${user.email.split('@')[0]}`)
              )
            ),
            React.createElement('div', { className: 'flex items-center space-x-3' },
              React.createElement('img', {
                src: window.AppImages.chefImage,
                alt: 'Chef',
                className: 'w-12 h-12 rounded-full'
              }),
              React.createElement('button', {
                className: 'bg-blue-50 hover:bg-blue-100 text-blue-700 px-3 py-2 rounded-lg transition-colors font-medium text-sm',
                onClick: () => setShowSettings(true)
              }, '⚙️ Settings'),
              React.createElement('button', {
                className: 'bg-red-50 hover:bg-red-100 text-red-700 px-3 py-2 rounded-lg transition-colors font-medium text-sm',
                onClick: ()=>auth.signOut()
              }, 'Logout')
            )
          )
        ),
        surveys.length === 0 ?
          React.createElement('div', { className: 'bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center' },
            React.createElement('div', { className: 'text-6xl mb-4' }, '🍽️'),
            React.createElement('h3', { className: 'text-lg font-semibold text-yellow-800 mb-2' }, 'No Menu Available'),
            React.createElement('p', { className: 'text-yellow-700' }, 'Tomorrow\'s menu hasn\'t been posted yet.'),
            React.createElement('p', { className: 'text-yellow-600 text-sm mt-2' }, 'Check back later or contact the kitchen staff.')
          ) :
        surveys.map(s => {
          const hasVoted = !!userVotes[s.id];
          const now = new Date();
          const votingDeadline = s.votingDeadline ? s.votingDeadline.toDate() : null;
          const participationDeadline = s.participationDeadline ? s.participationDeadline.toDate() : null;

          // Determine current phase and permissions
          const currentPhase = s.phase || 'voting';
          const canVote = currentPhase === 'voting' && (!votingDeadline || now <= votingDeadline);
          const showResults = currentPhase === 'results' || currentPhase === 'participation' || currentPhase === 'closed';
          const canParticipate = (currentPhase === 'participation' || currentPhase === 'results') &&
                                (!participationDeadline || now <= participationDeadline);

          // Check if current time is past midnight (participation deadline)
          const currentHour = now.getHours();
          const currentMinute = now.getMinutes();
          const currentTime = currentHour * 60 + currentMinute;
          const midnightDeadline = 23 * 60 + 59; // 23:59
          const participationOpen = currentTime <= midnightDeadline;

          return React.createElement('div', {
            key: s.id,
            className: `bg-white rounded-xl shadow-lg p-6 mb-6 border-l-4 ${
              currentPhase === 'voting' ? 'border-blue-500' :
              currentPhase === 'results' ? 'border-yellow-500' :
              currentPhase === 'participation' ? 'border-green-500' :
              'border-gray-500'
            }`
          },
            React.createElement('div', { className: 'flex justify-between items-start mb-3' },
              React.createElement('h2', { className: 'text-2xl font-bold text-gray-800' }, s.title),
              React.createElement('div', { className: 'flex space-x-2' },
                // Phase indicator
                React.createElement('span', {
                  className: `px-3 py-1 rounded-full text-sm font-semibold ${
                    currentPhase === 'voting' ? 'bg-blue-100 text-blue-800' :
                    currentPhase === 'results' ? 'bg-yellow-100 text-yellow-800' :
                    currentPhase === 'participation' ? 'bg-green-100 text-green-800' :
                    'bg-gray-100 text-gray-800'
                  }`
                },
                  currentPhase === 'voting' ? '🗳️ Voting Open' :
                  currentPhase === 'results' ? '🏆 Results' :
                  currentPhase === 'participation' ? '✋ Participation' :
                  '🔒 Closed'
                ),
                // Vote count
                s.totalVotes ? React.createElement('span', { className: 'bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold' }, `${s.totalVotes} votes`) : null
              )
            ),
            React.createElement('p', { className: 'text-gray-600 mb-4 text-lg' }, s.description),

            // Time information
            React.createElement('div', { className: 'bg-gray-50 rounded-lg p-4 mb-6' },
              React.createElement('div', { className: 'grid grid-cols-1 md:grid-cols-2 gap-4 text-sm' },
                votingDeadline ? React.createElement('div', null,
                  React.createElement('span', { className: 'font-semibold text-gray-700' }, '🗳️ Voting closes: '),
                  React.createElement('span', { className: canVote ? 'text-blue-600' : 'text-red-600' },
                    votingDeadline.toLocaleString('en-US', {
                      weekday: 'short', hour: '2-digit', minute: '2-digit'
                    })
                  )
                ) : null,
                participationDeadline ? React.createElement('div', null,
                  React.createElement('span', { className: 'font-semibold text-gray-700' }, '✋ Participation closes: '),
                  React.createElement('span', { className: canParticipate ? 'text-green-600' : 'text-red-600' },
                    participationDeadline.toLocaleString('en-US', {
                      weekday: 'short', hour: '2-digit', minute: '2-digit'
                    })
                  )
                ) : null
              )
            ),

            // Winning meal display (when in results or participation phase)
            showResults ? React.createElement('div', {
              className: `bg-gradient-to-r ${s.thresholdMet ? 'from-yellow-50 to-orange-50 border-yellow-300' : 'from-red-50 to-orange-50 border-orange-300'} border-2 rounded-lg p-6 mb-6`
            },
              React.createElement('div', { className: 'text-center' },
                React.createElement('div', { className: 'text-4xl mb-2' }, s.thresholdMet ? '🏆' : '⚠️'),
                React.createElement('h3', { className: `text-xl font-bold mb-2 ${s.thresholdMet ? 'text-yellow-800' : 'text-orange-800'}` },
                  s.thresholdMet ? 'Tomorrow\'s Winning Meal!' : 'Tomorrow\'s Menu (Low Participation)'
                ),
                s.winningOption ? React.createElement('div', { className: 'bg-white rounded-lg p-4 shadow-inner mb-3' },
                  React.createElement('div', { className: 'flex items-center justify-center space-x-3' },
                    React.createElement('span', { className: 'text-3xl' }, getFoodEmoji(s.winningOption.text)),
                    React.createElement('span', { className: 'text-2xl font-bold text-gray-800' }, s.winningOption.text)
                  )
                ) : React.createElement('div', { className: 'bg-white rounded-lg p-4 shadow-inner mb-3' },
                  React.createElement('span', { className: 'text-gray-500 italic' }, 'Determining winning option...')
                ),
                React.createElement('div', { className: 'flex justify-center space-x-4 text-sm' },
                  React.createElement('span', { className: 'text-gray-600' }, `${s.totalVotes} total votes`),
                  React.createElement('span', { className: s.thresholdMet ? 'text-green-600 font-semibold' : 'text-red-600 font-semibold' },
                    s.thresholdMet ? '✅ Meal confirmed!' : `❌ Only ${s.totalVotes}/${s.votesRequired || 25} needed`
                  )
                ),
                React.createElement('p', { className: `text-sm mt-2 ${s.thresholdMet ? 'text-yellow-700' : 'text-orange-700'}` },
                  s.thresholdMet ? 'This delicious meal will be served tomorrow!' : 'Meal may be served with limited portions due to low participation.'
                )
              )
            ) : null,

            // Show progress bar only during voting phase
            currentPhase === 'voting' && s.threshold ? React.createElement('div', { className: 'mb-6' },
              React.createElement('div', { className: 'flex justify-between text-sm font-medium text-gray-700 mb-2' },
                React.createElement('span', null, 'Voting Progress'),
                React.createElement('span', null, `${Math.round(s.progressPercent)}%`)
              ),
              React.createElement('div', { className: 'w-full bg-gray-200 rounded-full h-3' },
                React.createElement('div', {
                  className: `h-3 rounded-full transition-all duration-300 ${s.thresholdMet ? 'bg-green-500' : 'bg-blue-500'}`,
                  style: { width: `${s.progressPercent}%` }
                })
              ),
              React.createElement('div', { className: 'flex justify-between text-sm text-gray-600 mt-2' },
                React.createElement('span', null, `${s.participantCount} will eat • ${s.totalVotes - s.participantCount} won't eat`),
                React.createElement('span', { className: s.thresholdMet ? 'text-green-600 font-semibold' : 'text-blue-600' },
                  s.thresholdMet ? 'Meal confirmed!' : `Need ${s.threshold - s.totalVotes} more votes`
                )
              )
            ) : null,

            // Voting options - only show during voting phase
            currentPhase === 'voting' && !hasVoted && canVote ? React.createElement('div', { className: 'grid gap-3 mt-6' },
              s.options.map((o, index) => React.createElement('button', {
                key: o.id,
                className: `text-left p-4 border-2 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 transform hover:scale-105 ${
                  index === 0 ? 'border-orange-200 bg-orange-50' :
                  index === 1 ? 'border-green-200 bg-green-50' :
                  index === 2 ? 'border-purple-200 bg-purple-50' :
                  index === 3 ? 'border-red-200 bg-red-50' :
                  'border-indigo-200 bg-indigo-50'
                }`,
                onClick: ()=>vote(s.id, o.id)
              },
                React.createElement('div', { className: 'flex items-center space-x-3' },
                  React.createElement('span', { className: 'text-2xl' }, getFoodEmoji(o.text)),
                  React.createElement('div', null,
                    React.createElement('div', { className: 'font-semibold text-gray-800' }, `${index + 1}. ${o.text}`),
                    React.createElement('div', { className: 'text-xs text-gray-500 mt-1' }, 'Click to vote for this delicious option')
                  )
                )
              ))
            ) : currentPhase === 'voting' && hasVoted ? React.createElement('div', { className: 'bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 mt-6' },
              React.createElement('div', { className: 'text-center' },
                React.createElement('div', { className: 'text-4xl mb-3' }, '🎉'),
                React.createElement('div', { className: 'flex items-center justify-center mb-2' },
                  React.createElement('span', { className: 'text-green-600 text-xl mr-2' }, '✓'),
                  React.createElement('span', { className: 'text-green-800 font-semibold' }, `You voted for: ${s.options.find(o=>o.id===userVotes[s.id].optionId)?.text}`)
                ),
                React.createElement('div', { className: 'bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-full inline-block font-bold text-lg shadow-lg' },
                  '🇹🇳 Bon appétit! 🇹🇳'
                ),
                React.createElement('p', { className: 'text-gray-600 text-sm mt-2' }, 'Thank you for voting! Enjoy your meal tomorrow.')
              )
            ) : currentPhase === 'voting' && !canVote ? React.createElement('div', { className: 'bg-gray-50 border border-gray-200 rounded-lg p-4 mt-6 text-center' },
              React.createElement('span', { className: 'text-gray-500 italic' }, 'Voting is currently closed')
            ) : null,

            // Participation section for voters (only during participation phase)
            hasVoted && showResults && participationOpen ? React.createElement('div', { className: 'mt-6 bg-gradient-to-r from-blue-50 to-green-50 border-2 border-blue-200 rounded-lg p-6' },
              React.createElement('h4', { className: 'font-bold text-gray-800 mb-4 text-center' }, '🍽️ Meal Participation Confirmation'),
              React.createElement('p', { className: 'text-gray-600 text-sm text-center mb-4' }, 'Please confirm if you will actually eat this meal tomorrow to help us reduce food waste.'),
              React.createElement('p', { className: 'text-orange-600 text-xs text-center mb-4 font-semibold' },
                '⏰ Participation closes at midnight (23:59)'
              ),

              React.createElement('div', { className: 'flex space-x-3' },
                // Confirm Participation Button
                React.createElement('button', {
                  className: `flex-1 py-3 px-4 rounded-lg font-semibold transition-all duration-200 ${
                    userVotes[s.id].participate
                      ? 'bg-green-600 text-white shadow-lg transform scale-105'
                      : 'bg-gray-200 text-gray-700 hover:bg-green-100 hover:text-green-700'
                  }`,
                  onClick: () => setParticipation(s.id, true)
                },
                  React.createElement('div', { className: 'text-center' },
                    React.createElement('div', { className: 'text-xl mb-1' }, '✅'),
                    React.createElement('div', { className: 'text-sm font-bold' }, 'YES, I\'LL EAT'),
                    React.createElement('div', { className: 'text-xs opacity-75' }, 'Count me in!')
                  )
                ),

                // Decline Participation Button
                React.createElement('button', {
                  className: `flex-1 py-3 px-4 rounded-lg font-semibold transition-all duration-200 ${
                    userVotes[s.id].participate === false
                      ? 'bg-red-600 text-white shadow-lg transform scale-105'
                      : 'bg-gray-200 text-gray-700 hover:bg-red-100 hover:text-red-700'
                  }`,
                  onClick: () => setParticipation(s.id, false)
                },
                  React.createElement('div', { className: 'text-center' },
                    React.createElement('div', { className: 'text-xl mb-1' }, '❌'),
                    React.createElement('div', { className: 'text-sm font-bold' }, 'NO, I WON\'T EAT'),
                    React.createElement('div', { className: 'text-xs opacity-75' }, 'Skip this meal')
                  )
                )
              ),

              // Status Display
              React.createElement('div', { className: 'mt-4 text-center' },
                userVotes[s.id].participate === true ?
                  React.createElement('div', { className: 'bg-green-100 text-green-800 px-4 py-2 rounded-full inline-block font-semibold' },
                    '🎉 Confirmed! You will eat this meal'
                  ) :
                userVotes[s.id].participate === false ?
                  React.createElement('div', { className: 'bg-red-100 text-red-800 px-4 py-2 rounded-full inline-block font-semibold' },
                    '⚠️ You won\'t eat this meal'
                  ) :
                  React.createElement('div', { className: 'bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full inline-block font-semibold' },
                    '⏳ Please confirm your participation'
                  )
              )
            ) : hasVoted && showResults && !participationOpen ? React.createElement('div', { className: 'mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4 text-center' },
              React.createElement('span', { className: 'text-gray-500 italic' }, '🔒 Participation period has ended (after midnight)')
            ) : null,

            // Second chance for non-voters (when in participation phase)
            !hasVoted && showResults && s.winningOption && participationOpen ? React.createElement('div', { className: 'mt-6 bg-gradient-to-r from-purple-50 to-pink-50 border-2 border-purple-300 rounded-lg p-6' },
              React.createElement('div', { className: 'text-center mb-4' },
                React.createElement('div', { className: 'text-4xl mb-2' }, '🎯'),
                React.createElement('h4', { className: 'font-bold text-purple-800 mb-2 text-lg' }, 'Second Chance!'),
                React.createElement('p', { className: 'text-purple-700 text-sm mb-3' }, 'You missed the voting, but you can still join tomorrow\'s meal:')
              ),

              React.createElement('div', { className: 'bg-white rounded-lg p-4 shadow-inner mb-4 border-2 border-purple-200' },
                React.createElement('div', { className: 'text-center' },
                  React.createElement('div', { className: 'text-3xl mb-2' }, getFoodEmoji(s.winningOption.text)),
                  React.createElement('div', { className: 'font-bold text-gray-800 text-xl' }, s.winningOption.text),
                  React.createElement('div', { className: 'text-purple-600 text-sm mt-1 font-semibold' }, 'Tomorrow\'s Winning Meal')
                )
              ),

              React.createElement('p', { className: 'text-orange-600 text-xs text-center mb-4 font-semibold' },
                '⏰ Last chance! Participation closes at midnight (23:59)'
              ),

              React.createElement('div', { className: 'flex space-x-3' },
                // Join Meal Button
                React.createElement('button', {
                  className: `flex-1 py-4 px-4 rounded-lg font-semibold transition-all duration-200 ${
                    userVotes[s.id]?.participate === true
                      ? 'bg-green-600 text-white shadow-lg transform scale-105'
                      : 'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 shadow-lg'
                  }`,
                  onClick: () => joinMeal(s.id, true)
                },
                  React.createElement('div', { className: 'text-center' },
                    React.createElement('div', { className: 'text-2xl mb-1' }, '✅'),
                    React.createElement('div', { className: 'text-sm font-bold' }, 'YES, I\'LL JOIN'),
                    React.createElement('div', { className: 'text-xs opacity-90' }, 'Count me in!')
                  )
                ),

                // Skip Meal Button
                React.createElement('button', {
                  className: `flex-1 py-4 px-4 rounded-lg font-semibold transition-all duration-200 ${
                    userVotes[s.id]?.participate === false
                      ? 'bg-red-600 text-white shadow-lg transform scale-105'
                      : 'bg-gray-300 text-gray-700 hover:bg-red-100 hover:text-red-700'
                  }`,
                  onClick: () => joinMeal(s.id, false)
                },
                  React.createElement('div', { className: 'text-center' },
                    React.createElement('div', { className: 'text-2xl mb-1' }, '❌'),
                    React.createElement('div', { className: 'text-sm font-bold' }, 'NO, I\'LL SKIP'),
                    React.createElement('div', { className: 'text-xs opacity-75' }, 'Not interested')
                  )
                )
              ),

              // Status Display for non-voters
              userVotes[s.id] ? React.createElement('div', { className: 'mt-4 text-center' },
                userVotes[s.id].participate === true ?
                  React.createElement('div', { className: 'bg-green-100 text-green-800 px-6 py-3 rounded-full inline-block font-bold text-sm' },
                    '🎉 Excellent! You will join tomorrow\'s meal'
                  ) :
                userVotes[s.id].participate === false ?
                  React.createElement('div', { className: 'bg-red-100 text-red-800 px-6 py-3 rounded-full inline-block font-bold text-sm' },
                    '⚠️ You won\'t join tomorrow\'s meal'
                  ) : null
              ) : null
            ) : !hasVoted && showResults && s.winningOption && !participationOpen ? React.createElement('div', { className: 'mt-6 bg-gray-50 border border-gray-200 rounded-lg p-6 text-center' },
              React.createElement('div', { className: 'text-4xl mb-2' }, '🔒'),
              React.createElement('h4', { className: 'font-bold text-gray-600 mb-2' }, 'Participation Closed'),
              React.createElement('p', { className: 'text-gray-500 text-sm' }, 'The participation period ended at midnight. Better luck next time!')
            ) : null
          );
        }),

        // Settings modal
        showSettings && React.createElement(UserSettings, {
          user: user,
          onClose: () => setShowSettings(false)
        })
      )
    );
  }

  function App(){
    const [user, setUser] = useState(null);
    const [verified, setVerified] = useState(false);
    useEffect(()=> auth.onAuthStateChanged(async (u)=>{
      setUser(u);
      setVerified(!!u && u.emailVerified);
      // Refresh token to reflect verification after clicking email link
      if (u) await u.reload();
    }),[]);

    if (!user) return React.createElement(Login, { onLoggedIn: ()=>{} });
    if (!verified) return React.createElement('div', { className: 'min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-green-50' },
      React.createElement('div', { className: 'bg-white p-8 rounded-xl shadow-xl max-w-md mx-4 border-t-4 border-orange-500' },
        React.createElement('div', { className: 'text-center mb-6' },
          React.createElement('div', { className: 'w-20 h-20 mx-auto mb-3 rounded-xl shadow-lg bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center border-2 border-red-500' },
            React.createElement('div', { className: 'text-center' },
              React.createElement('div', { className: 'text-white font-bold text-lg' }, 'ISA'),
              React.createElement('div', { className: 'text-white text-xs' }, 'Restaurant'),
              React.createElement('div', { className: 'text-lg' }, '🍽️')
            )
          ),
          React.createElement('div', { className: 'text-4xl mb-3' }, '📧'),
          React.createElement('h2', { className: 'text-xl font-bold text-gray-800 mb-2' }, 'Verify Your Email'),
          React.createElement('p', { className: 'text-gray-600' }, 'Please check your inbox and verify your email to start voting.')
        ),
        React.createElement('button', {
          className: 'w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg font-semibold transition-colors mb-4',
          onClick: async ()=>{ if (auth.currentUser) await auth.currentUser.sendEmailVerification(); }
        }, 'Resend Verification Email'),
        React.createElement('div', { className: 'text-center' },
          React.createElement('button', {
            className: 'text-gray-600 hover:text-gray-800 text-sm',
            onClick: ()=>auth.signOut()
          }, 'Sign out')
        )
      )
    );

    return React.createElement(SurveyApp, { user });
  }

  // User Profile Settings Component
  function UserSettings({ user, onClose }) {
    const [profile, setProfile] = useState(null);
    const [phoneNumber, setPhoneNumber] = useState('');
    const [notifications, setNotifications] = useState({
      enabled: true,
      sms: false,
      email: true,
      voteReminder: true,
      resultAnnouncement: true,
      participationReminder: true
    });
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);

    useEffect(() => {
      loadUserProfile();
    }, []);

    const loadUserProfile = async () => {
      try {
        const profileDoc = await db.collection('userProfiles').doc(user.uid).get();
        if (profileDoc.exists) {
          const data = profileDoc.data();
          setProfile(data);
          setPhoneNumber(data.phoneNumber ? data.phoneNumber.replace('+216', '') : '');
          setNotifications(data.notifications || notifications);
        }
        setLoading(false);
      } catch (error) {
        console.error('Error loading profile:', error);
        setLoading(false);
      }
    };

    const saveProfile = async () => {
      setSaving(true);
      try {
        const profileData = {
          email: user.email,
          phoneNumber: phoneNumber ? `+216${phoneNumber}` : null,
          notifications: notifications,
          updatedAt: firebase.firestore.FieldValue.serverTimestamp()
        };

        await db.collection('userProfiles').doc(user.uid).set(profileData, { merge: true });
        alert('✅ Profile updated successfully!');
      } catch (error) {
        console.error('Error saving profile:', error);
        alert('❌ Error saving profile: ' + error.message);
      }
      setSaving(false);
    };

    const isValidTunisianPhone = (phone) => {
      const tunisianMobileRegex = /^[2459]\d{7}$/;
      return tunisianMobileRegex.test(phone);
    };

    if (loading) {
      return React.createElement('div', { className: 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50' },
        React.createElement('div', { className: 'bg-white rounded-xl p-6' },
          React.createElement('div', { className: 'text-center' }, 'Loading profile...')
        )
      );
    }

    return React.createElement('div', { className: 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4' },
      React.createElement('div', { className: 'bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto' },
        // Header
        React.createElement('div', { className: 'flex justify-between items-center p-6 border-b' },
          React.createElement('h2', { className: 'text-xl font-bold text-gray-800' }, '⚙️ Profile Settings'),
          React.createElement('button', {
            onClick: onClose,
            className: 'text-gray-500 hover:text-gray-700 text-2xl'
          }, '×')
        ),

        // Content
        React.createElement('div', { className: 'p-6 space-y-6' },
          // User Info
          React.createElement('div', { className: 'bg-blue-50 rounded-lg p-4' },
            React.createElement('h3', { className: 'font-semibold text-blue-800 mb-2' }, '👤 Account Info'),
            React.createElement('p', { className: 'text-sm text-blue-700' }, `Email: ${user.email}`)
          ),

          // Phone Number
          React.createElement('div', { className: 'space-y-2' },
            React.createElement('label', { className: 'block text-sm font-medium text-gray-700' }, '📱 Phone Number (Optional)'),
            React.createElement('div', { className: 'flex' },
              React.createElement('div', { className: 'flex items-center px-3 py-2 bg-gray-50 border border-r-0 rounded-l-lg text-sm text-gray-600' },
                '🇹🇳 +216'
              ),
              React.createElement('input', {
                className: 'flex-1 px-3 py-2 border rounded-r-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                type: 'tel', placeholder: '********', value: phoneNumber,
                onChange: (e) => setPhoneNumber(e.target.value.replace(/\D/g, '').slice(0, 8)),
                maxLength: 8
              })
            ),
            React.createElement('p', { className: 'text-xs text-gray-500' },
              'For SMS notifications (8 digits, starts with 2, 4, 5, or 9)'
            ),
            phoneNumber && !isValidTunisianPhone(phoneNumber) &&
              React.createElement('p', { className: 'text-xs text-red-500' },
                '⚠️ Invalid phone number format'
              )
          ),

          // Notification Settings
          React.createElement('div', { className: 'space-y-4' },
            React.createElement('h3', { className: 'font-semibold text-gray-800' }, '🔔 Notification Preferences'),

            // Master toggle
            React.createElement('label', { className: 'flex items-center space-x-3 p-3 bg-gray-50 rounded-lg' },
              React.createElement('input', {
                type: 'checkbox',
                checked: notifications.enabled,
                onChange: (e) => setNotifications({...notifications, enabled: e.target.checked}),
                className: 'w-4 h-4 text-blue-600 rounded focus:ring-blue-500'
              }),
              React.createElement('div', { className: 'flex-1' },
                React.createElement('div', { className: 'text-sm font-medium text-gray-800' }, 'Enable All Notifications'),
                React.createElement('div', { className: 'text-xs text-gray-600' }, 'Master switch for all notifications')
              )
            ),

            // Individual notification types
            notifications.enabled && React.createElement('div', { className: 'space-y-3 ml-4' },
              // SMS notifications
              phoneNumber && React.createElement('label', { className: 'flex items-center space-x-3' },
                React.createElement('input', {
                  type: 'checkbox',
                  checked: notifications.sms,
                  onChange: (e) => setNotifications({...notifications, sms: e.target.checked}),
                  className: 'w-4 h-4 text-green-600 rounded focus:ring-green-500'
                }),
                React.createElement('span', { className: 'text-sm text-gray-700' }, '📱 SMS Notifications')
              ),

              // Email notifications
              React.createElement('label', { className: 'flex items-center space-x-3' },
                React.createElement('input', {
                  type: 'checkbox',
                  checked: notifications.email,
                  onChange: (e) => setNotifications({...notifications, email: e.target.checked}),
                  className: 'w-4 h-4 text-blue-600 rounded focus:ring-blue-500'
                }),
                React.createElement('span', { className: 'text-sm text-gray-700' }, '📧 Email Notifications')
              ),

              // Vote reminders
              React.createElement('label', { className: 'flex items-center space-x-3' },
                React.createElement('input', {
                  type: 'checkbox',
                  checked: notifications.voteReminder,
                  onChange: (e) => setNotifications({...notifications, voteReminder: e.target.checked}),
                  className: 'w-4 h-4 text-orange-600 rounded focus:ring-orange-500'
                }),
                React.createElement('span', { className: 'text-sm text-gray-700' }, '⏰ Vote Reminders')
              ),

              // Result announcements
              React.createElement('label', { className: 'flex items-center space-x-3' },
                React.createElement('input', {
                  type: 'checkbox',
                  checked: notifications.resultAnnouncement,
                  onChange: (e) => setNotifications({...notifications, resultAnnouncement: e.target.checked}),
                  className: 'w-4 h-4 text-green-600 rounded focus:ring-green-500'
                }),
                React.createElement('span', { className: 'text-sm text-gray-700' }, '🎉 Meal Results')
              ),

              // Participation reminders
              React.createElement('label', { className: 'flex items-center space-x-3' },
                React.createElement('input', {
                  type: 'checkbox',
                  checked: notifications.participationReminder,
                  onChange: (e) => setNotifications({...notifications, participationReminder: e.target.checked}),
                  className: 'w-4 h-4 text-purple-600 rounded focus:ring-purple-500'
                }),
                React.createElement('span', { className: 'text-sm text-gray-700' }, '🍽️ Participation Reminders')
              )
            )
          )
        ),

        // Footer
        React.createElement('div', { className: 'flex space-x-3 p-6 border-t' },
          React.createElement('button', {
            onClick: onClose,
            className: 'flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50'
          }, 'Cancel'),
          React.createElement('button', {
            onClick: saveProfile,
            disabled: saving || (phoneNumber && !isValidTunisianPhone(phoneNumber)),
            className: `flex-1 px-4 py-2 rounded-lg text-white font-semibold ${
              saving || (phoneNumber && !isValidTunisianPhone(phoneNumber))
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
            }`
          }, saving ? 'Saving...' : 'Save Settings')
        )
      )
    );
  }

  ReactDOM.createRoot(document.getElementById('root')).render(React.createElement(App));
})();

