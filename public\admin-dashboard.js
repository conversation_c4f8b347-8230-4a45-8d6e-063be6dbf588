// Admin Dashboard JavaScript
console.log('Admin dashboard script loading...');

// Admin credentials (in production, store this securely)
const ADMIN_PASSWORD = "Z62342";

let app, auth, db;

// Initialize Firebase
try {
    if (typeof firebase !== 'undefined' && window.firebaseConfig) {
        app = firebase.initializeApp(window.firebaseConfig);
        auth = firebase.auth();
        db = firebase.firestore();
        console.log('Firebase initialized successfully');
    } else {
        console.error('Firebase or config not available');
    }
} catch (error) {
    console.error('Error initializing Firebase:', error);
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking auth...');
    checkAdminAuth();
});

// Admin Authentication
function checkAdminAuth() {
    console.log('Checking admin authentication...');
    const isAuthenticated = sessionStorage.getItem('adminAuthenticated');
    console.log('Is authenticated:', isAuthenticated);

    if (!isAuthenticated) {
        showLoginModal();
    } else {
        initializeDashboard();
    }
}

function showLoginModal() {
    document.body.innerHTML = `
        <div class="min-h-screen bg-gray-100 flex items-center justify-center">
            <div class="bg-white p-8 rounded-xl shadow-xl max-w-md w-full mx-4">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 mx-auto mb-3 rounded-xl shadow-md bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center border-2 border-red-500">
                        <div class="text-center">
                            <div class="text-white font-bold text-lg">ISA</div>
                            <div class="text-xs">👨‍💼</div>
                        </div>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800">Admin Access</h2>
                    <p class="text-gray-600">Enter admin password to continue</p>
                </div>
                <div id="loginError" class="mb-4 text-red-600 text-sm text-center hidden"></div>
                <div class="space-y-4">
                    <input type="password" id="adminPassword" placeholder="Admin Password"
                           class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           onkeypress="if(event.key==='Enter') adminLogin()">
                    <button onclick="adminLogin()" class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 font-semibold">
                        🔓 Access Dashboard
                    </button>
                </div>
                <div class="mt-4 text-center">
                    <a href="https://isa-meal-vote.web.app" class="text-blue-600 hover:text-blue-800 text-sm">
                        ← Back to Main App
                    </a>
                </div>
            </div>
        </div>
    `;
}

function adminLogin() {
    const password = document.getElementById('adminPassword').value;
    const errorDiv = document.getElementById('loginError');

    if (password === ADMIN_PASSWORD) {
        sessionStorage.setItem('adminAuthenticated', 'true');
        location.reload(); // Reload to show dashboard
    } else {
        errorDiv.textContent = 'Invalid password. Please try again.';
        errorDiv.classList.remove('hidden');
        document.getElementById('adminPassword').value = '';
        document.getElementById('adminPassword').focus();
    }
}

async function initializeDashboard() {
    console.log('Initializing dashboard...');

    try {
        // Test Firebase connection first
        if (db) {
            console.log('Testing Firebase connection...');
            const testQuery = await db.collection('surveys').limit(1).get();
            console.log('Firebase connection successful, found surveys:', testQuery.size);

            // Load data
            await loadOverviewData();
            showTab('overview');
        } else {
            console.error('Database not initialized');
            alert('Database connection failed. Please refresh the page.');
        }
    } catch (error) {
        console.error('Error initializing dashboard:', error);
        alert('Error initializing dashboard: ' + error.message);
    }
}

async function testFirebaseConnection() {
    try {
        console.log('Testing Firebase connection...');
        console.log('Firebase config:', window.firebaseConfig);

        // Test basic Firestore connection
        const testDoc = await db.collection('test').limit(1).get();
        console.log('Firebase connection successful');

        // Test surveys collection
        const surveysTest = await db.collection('surveys').limit(1).get();
        console.log('Surveys collection accessible:', surveysTest.size >= 0);

        showStatus('Firebase connection successful', 'success');

    } catch (error) {
        console.error('Firebase connection failed:', error);
        showStatus('Firebase connection failed: ' + error.message, 'error');

        // Show detailed error information
        const errorInfo = `
            Error: ${error.message}
            Code: ${error.code || 'Unknown'}

            Please check:
            1. Firebase configuration
            2. Firestore rules
            3. Network connection
        `;

        alert('Firebase Connection Error:\n' + errorInfo);
    }
}

// Make adminLogout globally accessible
window.adminLogout = function() {
    console.log('Logout function called');
    if (confirm('Are you sure you want to logout from the admin dashboard?')) {
        console.log('User confirmed logout');
        sessionStorage.removeItem('adminAuthenticated');
        showStatus('Logging out...', 'info');

        // Change button text to show it's working
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.innerHTML = '⏳ Logging out...';
            logoutBtn.disabled = true;
        }

        setTimeout(() => {
            location.reload();
        }, 1500);
    } else {
        console.log('User cancelled logout');
    }
}

// Tab Management - Make globally accessible
window.showTab = function(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });

    // Remove active class from all tabs
    document.querySelectorAll('[id^="tab-"]').forEach(tab => {
        tab.classList.remove('border-blue-500', 'text-blue-600');
        tab.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show selected tab content
    document.getElementById(`content-${tabName}`).classList.remove('hidden');
    
    // Add active class to selected tab
    const activeTab = document.getElementById(`tab-${tabName}`);
    activeTab.classList.remove('border-transparent', 'text-gray-500');
    activeTab.classList.add('border-blue-500', 'text-blue-600');
    
    // Load tab-specific data
    switch(tabName) {
        case 'users':
            console.log('Loading users tab...');
            loadUsers();
            break;
        case 'emails':
            console.log('Loading emails tab...');
            loadAuthorizedEmails();
            break;
        case 'settings':
            console.log('Loading settings tab...');
            loadSettings();
            break;
    }
}

// Make key functions globally accessible
window.testFirebaseConnection = testFirebaseConnection;
window.syncAllData = syncAllData;
window.loadUsers = loadUsers;
window.refreshUsers = refreshUsers;
window.loadAuthorizedEmails = loadAuthorizedEmails;
window.addAuthorizedEmail = addAuthorizedEmail;
window.removeAuthorizedEmail = removeAuthorizedEmail;
window.refreshAuthorizedEmails = refreshAuthorizedEmails;
window.importEmails = importEmails;
window.importUsersFromDatabase = importUsersFromDatabase;
window.exportEmails = exportEmails;
window.exportVotes = exportVotes;
window.clearAllEmails = clearAllEmails;
window.resetAllVotes = resetAllVotes;
window.saveSettings = saveSettings;
window.sendNotification = sendNotification;
window.viewUserDetails = viewUserDetails;
window.removeUser = removeUser;

// Overview Data
async function loadOverviewData() {
    console.log('Loading overview data...');

    if (!db) {
        console.error('Database not initialized');
        return;
    }

    try {
        // Set loading state
        document.getElementById('totalUsers').textContent = 'Loading...';
        document.getElementById('activeSurveys').textContent = 'Loading...';
        document.getElementById('todayParticipants').textContent = 'Loading...';

        // Get surveys
        console.log('Fetching surveys...');
        const surveysSnap = await db.collection('surveys').get();
        console.log('Found surveys:', surveysSnap.size);

        if (surveysSnap.empty) {
            console.log('No surveys found');
            document.getElementById('totalUsers').textContent = '0';
            document.getElementById('activeSurveys').textContent = '0';
            document.getElementById('todayParticipants').textContent = '0';
            return;
        }

        let uniqueUsers = new Set();
        let todayParticipants = 0;
        let activeSurveyCount = 0;

        // Process each survey
        for (const surveyDoc of surveysSnap.docs) {
            const surveyData = surveyDoc.data();

            // Count active surveys
            if (surveyData.phase !== 'closed') {
                activeSurveyCount++;
            }

            try {
                // Get votes for this survey
                const votesSnap = await db.collection('surveys').doc(surveyDoc.id).collection('votes').get();
                console.log(`Survey ${surveyDoc.id} has ${votesSnap.size} votes`);

                votesSnap.docs.forEach(voteDoc => {
                    const userId = voteDoc.id;
                    const voteData = voteDoc.data();

                    uniqueUsers.add(userId);

                    // Count today's participants
                    if (voteData.participate === true) {
                        todayParticipants++;
                    }
                });
            } catch (voteError) {
                console.error(`Error loading votes for survey ${surveyDoc.id}:`, voteError);
            }
        }

        // Update UI
        document.getElementById('totalUsers').textContent = uniqueUsers.size;
        document.getElementById('activeSurveys').textContent = activeSurveyCount;
        document.getElementById('todayParticipants').textContent = todayParticipants;

        console.log('Overview data loaded successfully');
        console.log('Total users:', uniqueUsers.size);
        console.log('Active surveys:', activeSurveyCount);
        console.log('Today participants:', todayParticipants);

        // Process each survey to get users and participants
        for (const surveyDoc of surveysSnap.docs) {
            const surveyData = surveyDoc.data();
            console.log('Processing survey:', surveyDoc.id);

            try {
                const votesSnap = await db.collection('surveys').doc(surveyDoc.id).collection('votes').get();
                console.log('Votes in', surveyDoc.id, ':', votesSnap.size);

                votesSnap.docs.forEach(voteDoc => {
                    const userId = voteDoc.id;
                    const voteData = voteDoc.data();

                    // Add to unique users
                    uniqueUsers.add(userId);

                    // Check if this is today's participant
                    const isToday = surveyData.createdAt && surveyData.createdAt.toDate() >= today;
                    if (isToday && voteData.participate === true) {
                        todayParticipants++;
                    }
                });
            } catch (voteError) {
                console.error('Error loading votes for survey', surveyDoc.id, ':', voteError);
            }
        }

        document.getElementById('totalUsers').textContent = uniqueUsers.size;
        document.getElementById('todayParticipants').textContent = todayParticipants;

        console.log('Overview data loaded successfully');
        console.log('Total users:', uniqueUsers.size);
        console.log('Active surveys:', activeSurveys.length);
        console.log('Today participants:', todayParticipants);

        // Load recent activity
        await loadRecentActivity();

    } catch (error) {
        console.error('Error loading overview data:', error);
        console.error('Error details:', error);
        showStatus('Error loading overview data: ' + error.message, 'error');

        // Set default values on error
        document.getElementById('totalUsers').textContent = 'Error';
        document.getElementById('activeSurveys').textContent = 'Error';
        document.getElementById('todayParticipants').textContent = 'Error';

        // Show detailed error in console for debugging
        console.log('Firebase config:', window.firebaseConfig);
        console.log('Database instance:', db);
    }
}

async function loadRecentActivity() {
    try {
        const activities = [];
        
        // Get recent surveys
        const surveysSnap = await db.collection('surveys').orderBy('createdAt', 'desc').limit(5).get();
        surveysSnap.docs.forEach(doc => {
            const data = doc.data();
            activities.push({
                type: 'survey',
                message: `Survey created: ${data.title}`,
                time: data.createdAt ? data.createdAt.toDate() : new Date(),
                icon: '📊'
            });
        });
        
        // Get recent votes
        const allVotes = await db.collectionGroup('votes').orderBy('createdAt', 'desc').limit(10).get();
        allVotes.docs.forEach(doc => {
            const data = doc.data();
            activities.push({
                type: 'vote',
                message: `New vote received`,
                time: data.createdAt ? data.createdAt.toDate() : new Date(),
                icon: '🗳️'
            });
        });
        
        // Sort by time and take latest 10
        activities.sort((a, b) => b.time - a.time);
        const recentActivities = activities.slice(0, 10);
        
        const activityContainer = document.getElementById('recentActivity');
        if (recentActivities.length === 0) {
            activityContainer.innerHTML = '<div class="text-gray-500 text-center py-4">No recent activity</div>';
            return;
        }
        
        activityContainer.innerHTML = recentActivities.map(activity => `
            <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <span class="text-xl">${activity.icon}</span>
                <div class="flex-1">
                    <p class="text-sm text-gray-800">${activity.message}</p>
                    <p class="text-xs text-gray-500">${activity.time.toLocaleString()}</p>
                </div>
            </div>
        `).join('');
        
    } catch (error) {
        console.error('Error loading recent activity:', error);
    }
}

// User Management
async function loadUsers() {
    console.log('Loading users...');

    if (!db) {
        console.error('Database not initialized');
        return;
    }

    try {
        const usersTable = document.getElementById('usersTable');
        usersTable.innerHTML = '<tr><td colspan="4" class="px-6 py-4 text-center text-gray-500">Loading users...</td></tr>';

        const usersData = new Map();

        // Get all surveys first
        const surveysSnap = await db.collection('surveys').get();
        console.log('Found surveys for user loading:', surveysSnap.size);

        if (surveysSnap.empty) {
            usersTable.innerHTML = '<tr><td colspan="4" class="px-6 py-4 text-center text-gray-500">No surveys found</td></tr>';
            return;
        }

        // Get votes from each survey
        for (const surveyDoc of surveysSnap.docs) {
            try {
                const votesSnap = await db.collection('surveys').doc(surveyDoc.id).collection('votes').get();
                console.log(`Survey ${surveyDoc.id} has ${votesSnap.size} votes`);

                votesSnap.docs.forEach(voteDoc => {
                    const userId = voteDoc.id;
                    const voteData = voteDoc.data();
                    const surveyId = surveyDoc.id;

                    // Try to get email from vote data or use a more readable format
                    let userEmail = userId;

                    // If the vote data contains email information, use it
                    if (voteData.userEmail) {
                        userEmail = voteData.userEmail;
                    } else if (voteData.email) {
                        userEmail = voteData.email;
                    } else {
                        // Format the UID to be more readable
                        userEmail = `User-${userId.substring(0, 8)}...`;
                    }

                    if (!usersData.has(userId)) {
                        usersData.set(userId, {
                            id: userId,
                            email: userEmail,
                            displayName: userEmail,
                            votes: [],
                            lastVote: null
                        });
                    }

                    const userData = usersData.get(userId);
                    userData.votes.push({ surveyId, ...voteData });

                    if (!userData.lastVote || (voteData.createdAt && voteData.createdAt.toDate() > userData.lastVote)) {
                        userData.lastVote = voteData.createdAt ? voteData.createdAt.toDate() : null;
                    }
                });
            } catch (voteError) {
                console.error(`Error loading votes for survey ${surveyDoc.id}:`, voteError);
            }
        }

        console.log('Total users found:', usersData.size);

        if (usersData.size === 0) {
            usersTable.innerHTML = '<tr><td colspan="4" class="px-6 py-4 text-center text-gray-500">No users found</td></tr>';
            return;
        }

        const usersArray = Array.from(usersData.values());

        // Try to get real email addresses from Firebase Auth if available
        for (const user of usersArray) {
            try {
                // Check if we can get user info from Firebase Auth
                if (auth && typeof auth.getUser === 'function') {
                    const userRecord = await auth.getUser(user.id);
                    if (userRecord && userRecord.email) {
                        user.email = userRecord.email;
                        user.displayName = userRecord.email;
                    }
                }
            } catch (error) {
                // Ignore auth errors, keep the formatted display name
                console.log(`Could not get email for user ${user.id}:`, error.message);
            }
        }

        usersTable.innerHTML = usersArray.map(user => `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">${user.displayName || user.email}</div>
                    <div class="text-sm text-gray-500">${user.votes.length} votes • ID: ${user.id.substring(0, 8)}...</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        Active
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${user.lastVote ? user.lastVote.toLocaleString() : 'Never'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="viewUserDetails('${user.id}')" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                    <button onclick="removeUser('${user.id}')" class="text-red-600 hover:text-red-900">Remove</button>
                </td>
            </tr>
        `).join('');

        console.log('Users table updated successfully');

    } catch (error) {
        console.error('Error loading users:', error);
        console.error('Error details:', error);
        showStatus('Error loading users: ' + error.message, 'error');

        const usersTable = document.getElementById('usersTable');
        usersTable.innerHTML = `<tr><td colspan="4" class="px-6 py-4 text-center text-red-500">Error loading users: ${error.message}</td></tr>`;
    }
}

function refreshUsers() {
    loadUsers();
    showStatus('Users refreshed', 'success');
}

// Email Management
async function loadAuthorizedEmails() {
    try {
        console.log('Loading authorized emails...');
        // For now, we'll use a simple collection to store authorized emails
        const emailsSnap = await db.collection('authorizedEmails').get();
        console.log('Authorized emails loaded:', emailsSnap.size);
        const emailsList = document.getElementById('authorizedEmailsList');

        if (emailsSnap.empty) {
            emailsList.innerHTML = '<div class="text-gray-500 text-center py-4">No authorized emails found</div>';
            return;
        }
        
        emailsList.innerHTML = emailsSnap.docs.map(doc => {
            const data = doc.data();
            return `
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <span class="text-lg">${data.role === 'admin' ? '👨‍💼' : '👨‍🎓'}</span>
                        <div>
                            <p class="text-sm font-medium text-gray-800">${data.email}</p>
                            <p class="text-xs text-gray-500">${data.role} • Added ${data.createdAt ? data.createdAt.toDate().toLocaleDateString() : 'Unknown'}</p>
                        </div>
                    </div>
                    <button onclick="removeAuthorizedEmail('${doc.id}')" class="text-red-600 hover:text-red-800 text-sm">
                        🗑️ Remove
                    </button>
                </div>
            `;
        }).join('');
        
    } catch (error) {
        console.error('Error loading authorized emails:', error);
        console.error('Error details:', error);
        showStatus('Error loading authorized emails: ' + error.message, 'error');

        const emailsList = document.getElementById('authorizedEmailsList');
        emailsList.innerHTML = '<div class="text-red-500 text-center py-4">Error loading emails: ' + error.message + '</div>';
    }
}

async function addAuthorizedEmail() {
    const email = document.getElementById('newEmail').value.trim();
    const role = document.getElementById('emailRole').value;
    
    if (!email) {
        showStatus('Please enter an email address', 'error');
        return;
    }
    
    if (!email.includes('@')) {
        showStatus('Please enter a valid email address', 'error');
        return;
    }
    
    try {
        await db.collection('authorizedEmails').add({
            email: email.toLowerCase(),
            role: role,
            createdAt: firebase.firestore.FieldValue.serverTimestamp(),
            createdBy: 'admin'
        });
        
        document.getElementById('newEmail').value = '';
        document.getElementById('emailRole').value = 'student';
        
        showStatus('Email added successfully', 'success');
        loadAuthorizedEmails();
        
    } catch (error) {
        console.error('Error adding email:', error);
        showStatus('Error adding email', 'error');
    }
}

async function removeAuthorizedEmail(docId) {
    if (!confirm('Are you sure you want to remove this email?')) {
        return;
    }
    
    try {
        await db.collection('authorizedEmails').doc(docId).delete();
        showStatus('Email removed successfully', 'success');
        loadAuthorizedEmails();
    } catch (error) {
        console.error('Error removing email:', error);
        showStatus('Error removing email', 'error');
    }
}

function refreshAuthorizedEmails() {
    loadAuthorizedEmails();
    showStatus('Authorized emails refreshed', 'success');
}

// Settings Management
async function loadSettings() {
    try {
        const settingsDoc = await db.collection('settings').doc('system').get();
        if (settingsDoc.exists) {
            const settings = settingsDoc.data();
            document.getElementById('totalStudentsSetting').value = settings.totalStudents || 100;
            document.getElementById('votingDeadlineTime').value = settings.votingDeadlineTime || '13:30';
            document.getElementById('participationDeadlineTime').value = settings.participationDeadlineTime || '23:59';
            document.getElementById('emailRestriction').checked = settings.emailRestriction || false;
        }
    } catch (error) {
        console.error('Error loading settings:', error);
    }
}

async function saveSettings() {
    try {
        const settings = {
            totalStudents: parseInt(document.getElementById('totalStudentsSetting').value),
            votingDeadlineTime: document.getElementById('votingDeadlineTime').value,
            participationDeadlineTime: document.getElementById('participationDeadlineTime').value,
            emailRestriction: document.getElementById('emailRestriction').checked,
            updatedAt: firebase.firestore.FieldValue.serverTimestamp()
        };
        
        await db.collection('settings').doc('system').set(settings, { merge: true });
        showStatus('Settings saved successfully', 'success');
        
    } catch (error) {
        console.error('Error saving settings:', error);
        showStatus('Error saving settings', 'error');
    }
}

// Utility Functions
function showStatus(message, type = 'info') {
    const statusDiv = document.getElementById('statusMessage');
    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
    
    statusDiv.innerHTML = `
        <div class="${bgColor} text-white px-4 py-2 rounded-lg shadow-lg">
            ${message}
        </div>
    `;
    
    setTimeout(() => {
        statusDiv.innerHTML = '';
    }, 3000);
}

// Export Functions
async function exportEmails() {
    try {
        const emailsSnap = await db.collection('authorizedEmails').get();

        if (emailsSnap.empty) {
            showStatus('No emails to export', 'info');
            return;
        }

        const csvContent = 'Email,Role,Created Date,Created By\n' +
            emailsSnap.docs.map(doc => {
                const data = doc.data();
                const createdDate = data.createdAt ? data.createdAt.toDate().toLocaleDateString() : 'Unknown';
                return `${data.email},${data.role},${createdDate},${data.createdBy || 'Unknown'}`;
            }).join('\n');

        downloadCSV(csvContent, 'authorized-emails.csv');
        showStatus(`Exported ${emailsSnap.size} emails`, 'success');

    } catch (error) {
        console.error('Error exporting emails:', error);
        showStatus('Error exporting emails: ' + error.message, 'error');
    }
}

async function exportVotes() {
    try {
        const surveysSnap = await db.collection('surveys').get();
        let allVotes = [];

        for (const surveyDoc of surveysSnap.docs) {
            const surveyData = surveyDoc.data();
            const votesSnap = await db.collection('surveys').doc(surveyDoc.id).collection('votes').get();

            votesSnap.docs.forEach(voteDoc => {
                const voteData = voteDoc.data();
                const selectedOption = surveyData.options?.find(opt => opt.id === voteData.optionId);

                allVotes.push({
                    survey: surveyData.title || surveyDoc.id,
                    email: voteDoc.id,
                    selectedOption: selectedOption?.text || 'Unknown',
                    participate: voteData.participate,
                    voteDate: voteData.createdAt ? voteData.createdAt.toDate().toLocaleDateString() : 'Unknown',
                    voteTime: voteData.createdAt ? voteData.createdAt.toDate().toLocaleTimeString() : 'Unknown'
                });
            });
        }

        if (allVotes.length === 0) {
            showStatus('No votes to export', 'info');
            return;
        }

        const csvContent = 'Survey,Email,Selected Option,Will Participate,Vote Date,Vote Time\n' +
            allVotes.map(vote =>
                `"${vote.survey}","${vote.email}","${vote.selectedOption}",${vote.participate},${vote.voteDate},${vote.voteTime}`
            ).join('\n');

        downloadCSV(csvContent, 'all-votes.csv');
        showStatus(`Exported ${allVotes.length} votes`, 'success');

    } catch (error) {
        console.error('Error exporting votes:', error);
        showStatus('Error exporting votes: ' + error.message, 'error');
    }
}

function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Bulk Actions
async function importEmails() {
    const fileInput = document.getElementById('csvFile');
    const file = fileInput.files[0];

    if (!file) {
        showStatus('Please select a CSV file first', 'error');
        return;
    }

    try {
        const text = await file.text();
        const lines = text.split('\n').filter(line => line.trim());
        let imported = 0;

        for (const line of lines) {
            const [email, role = 'student'] = line.split(',').map(s => s.trim());
            if (email && email.includes('@')) {
                await db.collection('authorizedEmails').add({
                    email: email.toLowerCase(),
                    role: role.toLowerCase(),
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    createdBy: 'admin-import'
                });
                imported++;
            }
        }

        showStatus(`Successfully imported ${imported} emails`, 'success');
        loadAuthorizedEmails();
        fileInput.value = '';

    } catch (error) {
        console.error('Error importing emails:', error);
        showStatus('Error importing emails: ' + error.message, 'error');
    }
}

async function importUsersFromDatabase() {
    try {
        showStatus('Importing users from database...', 'info');

        // Get all unique users from votes
        const uniqueUsers = new Set();
        const surveysSnap = await db.collection('surveys').get();

        for (const surveyDoc of surveysSnap.docs) {
            const votesSnap = await db.collection('surveys').doc(surveyDoc.id).collection('votes').get();
            votesSnap.docs.forEach(voteDoc => {
                const userId = voteDoc.id;
                if (userId.includes('@')) { // It's an email
                    uniqueUsers.add(userId);
                }
            });
        }

        let imported = 0;
        for (const email of uniqueUsers) {
            // Check if email already exists
            const existingEmail = await db.collection('authorizedEmails')
                .where('email', '==', email.toLowerCase()).get();

            if (existingEmail.empty) {
                await db.collection('authorizedEmails').add({
                    email: email.toLowerCase(),
                    role: 'student',
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    createdBy: 'auto-import',
                    source: 'database'
                });
                imported++;
            }
        }

        showStatus(`Successfully imported ${imported} users from database`, 'success');
        loadAuthorizedEmails();

    } catch (error) {
        console.error('Error importing users from database:', error);
        showStatus('Error importing users: ' + error.message, 'error');
    }
}

async function clearAllEmails() {
    if (!confirm('Are you sure you want to clear all authorized emails? This cannot be undone.')) {
        return;
    }

    try {
        const emailsSnap = await db.collection('authorizedEmails').get();
        const batch = db.batch();

        emailsSnap.docs.forEach(doc => {
            batch.delete(doc.ref);
        });

        await batch.commit();
        showStatus(`Cleared ${emailsSnap.size} authorized emails`, 'success');
        loadAuthorizedEmails();

    } catch (error) {
        console.error('Error clearing emails:', error);
        showStatus('Error clearing emails: ' + error.message, 'error');
    }
}

async function resetAllVotes() {
    if (!confirm('Are you sure you want to reset all votes? This cannot be undone.')) {
        return;
    }

    try {
        const surveysSnap = await db.collection('surveys').get();
        let deletedVotes = 0;

        for (const surveyDoc of surveysSnap.docs) {
            const votesSnap = await db.collection('surveys').doc(surveyDoc.id).collection('votes').get();
            const batch = db.batch();

            votesSnap.docs.forEach(voteDoc => {
                batch.delete(voteDoc.ref);
            });

            await batch.commit();
            deletedVotes += votesSnap.size;
        }

        showStatus(`Reset ${deletedVotes} votes from all surveys`, 'success');
        loadOverviewData();
        loadUsers();

    } catch (error) {
        console.error('Error resetting votes:', error);
        showStatus('Error resetting votes: ' + error.message, 'error');
    }
}

// User Actions
async function viewUserDetails(userId) {
    try {
        // Get user's voting history
        const userVotes = [];
        const surveysSnap = await db.collection('surveys').get();

        for (const surveyDoc of surveysSnap.docs) {
            const voteDoc = await db.collection('surveys').doc(surveyDoc.id).collection('votes').doc(userId).get();
            if (voteDoc.exists) {
                const surveyData = surveyDoc.data();
                const voteData = voteDoc.data();
                const selectedOption = surveyData.options?.find(opt => opt.id === voteData.optionId);

                userVotes.push({
                    survey: surveyData.title || surveyDoc.id,
                    option: selectedOption?.text || 'Unknown',
                    participate: voteData.participate,
                    date: voteData.createdAt ? voteData.createdAt.toDate().toLocaleDateString() : 'Unknown'
                });
            }
        }

        const details = `
            User: ${userId}
            Total Votes: ${userVotes.length}

            Voting History:
            ${userVotes.map(vote =>
                `• ${vote.survey}: ${vote.option} (${vote.participate ? 'Will participate' : 'Won\'t participate'}) - ${vote.date}`
            ).join('\n')}
        `;

        alert(details);

    } catch (error) {
        console.error('Error viewing user details:', error);
        showStatus('Error loading user details: ' + error.message, 'error');
    }
}

async function removeUser(userId) {
    if (!confirm(`Are you sure you want to remove user ${userId} and all their votes? This cannot be undone.`)) {
        return;
    }

    try {
        // Remove user from authorized emails
        const emailQuery = await db.collection('authorizedEmails').where('email', '==', userId.toLowerCase()).get();
        const batch = db.batch();

        emailQuery.docs.forEach(doc => {
            batch.delete(doc.ref);
        });

        // Remove all votes by this user
        const surveysSnap = await db.collection('surveys').get();
        let deletedVotes = 0;

        for (const surveyDoc of surveysSnap.docs) {
            const voteDoc = await db.collection('surveys').doc(surveyDoc.id).collection('votes').doc(userId).get();
            if (voteDoc.exists) {
                batch.delete(voteDoc.ref);
                deletedVotes++;
            }
        }

        await batch.commit();

        showStatus(`Removed user ${userId} and ${deletedVotes} votes`, 'success');
        loadUsers();
        loadAuthorizedEmails();
        loadOverviewData();

    } catch (error) {
        console.error('Error removing user:', error);
        showStatus('Error removing user: ' + error.message, 'error');
    }
}

// Database Sync
async function syncAllData() {
    try {
        showStatus('Syncing database data...', 'info');

        // Count users in database
        const uniqueUsers = new Set();
        const surveysSnap = await db.collection('surveys').get();
        let totalVotes = 0;

        for (const surveyDoc of surveysSnap.docs) {
            const votesSnap = await db.collection('surveys').doc(surveyDoc.id).collection('votes').get();
            totalVotes += votesSnap.size;
            votesSnap.docs.forEach(voteDoc => {
                uniqueUsers.add(voteDoc.id);
            });
        }

        // Count authorized emails
        const authorizedSnap = await db.collection('authorizedEmails').get();

        // Update UI
        document.getElementById('dbUsers').textContent = uniqueUsers.size;
        document.getElementById('dbAuthorized').textContent = authorizedSnap.size;
        document.getElementById('dbVotes').textContent = totalVotes;

        showStatus('Database sync completed', 'success');

    } catch (error) {
        console.error('Error syncing data:', error);
        showStatus('Error syncing data: ' + error.message, 'error');
    }
}

// Notifications
async function sendNotification() {
    const message = document.getElementById('notificationMessage').value.trim();
    const target = document.getElementById('notificationTarget').value;

    if (!message) {
        showStatus('Please enter a notification message', 'error');
        return;
    }

    try {
        // Store notification in database for future implementation
        await db.collection('notifications').add({
            message: message,
            target: target,
            createdAt: firebase.firestore.FieldValue.serverTimestamp(),
            createdBy: 'admin',
            status: 'sent'
        });

        showStatus(`Notification sent to ${target}`, 'success');
        document.getElementById('notificationMessage').value = '';

    } catch (error) {
        console.error('Error sending notification:', error);
        showStatus('Error sending notification: ' + error.message, 'error');
    }
}
